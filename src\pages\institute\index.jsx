import React, { useState, Fragment, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { Button, Modal, Form, FormGroup } from 'react-bootstrap';
import InstituteService from 'service/api/instituteService.js';
import toastService from 'utils/toastService.js';
import useGeneralStore from 'store/generalStore.js';
import InstituteList from 'pages/institute/components/institute-table.jsx';

const Institute = () => {
  const { setData } = useGeneralStore();

  // State management
  const [institutesData, setInstitutesData] = useState([]);
  const [institutesTotal, setInstitutesTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedInstitute, setSelectedInstitute] = useState(null);

  // Form state
  const [formData, setFormData] = useState({
    title: '',
    has_working_group: false,
    is_ministerial: false,
  });

  const [formErrors, setFormErrors] = useState({});

  // Pagination and search state
  const [page, setPage] = useState(1);
  const [order, setOrder] = useState({ value: 'desc', label: 'جدیدترین' });
  const [query, setQuery] = useState('');
  const pageSize = 12;

  // Fetch institutes from API
  const fetchInstituteData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await InstituteService.getList({
        q: query,
        page,
        order: order.value,
        page_size: pageSize,
      });
      const institutes = response?.data?.data?.institutes || [];
      setInstitutesData(institutes);
      setInstitutesTotal(response?.data?.data?.total || 0);
    } catch (error) {
      console.error('Error fetching institutes:', error);
      setInstitutesData([]);
      setInstitutesTotal(0);
      toastService.error(error?.response?.data?.data?.[0] || 'خطا در دریافت لیست موسسات!');
    } finally {
      setLoading(false);
    }
  }, [query, page, order.value, pageSize]);

  useEffect(() => {
    setData({ pageTitle: 'مدیریت موسسات' });
  }, [setData]);

  useEffect(() => {
    fetchInstituteData();
  }, [fetchInstituteData]);

  // Form validation
  const validateForm = () => {
    const errors = {};

    if (!formData.title.trim()) {
      errors.title = 'عنوان موسسه الزامی است';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));

    // Clear error when user starts typing
    if (formErrors[name]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      title: '',
      has_working_group: false,
      is_ministerial: false,
    });
    setFormErrors({});
    setEditMode(false);
    setSelectedInstitute(null);
  };

  // Handle modal close
  const handleClose = () => {
    setShow(false);
    resetForm();
  };

  // Handle modal show for add
  const handleShow = () => {
    resetForm();
    setShow(true);
  };

  // Handle edit institute
  // Handle edit institute
  const handleEditInstitute = useCallback((institute) => {
    setFormData({
      title: institute.title || '',
      has_working_group: institute.has_working_group || false,
      is_ministerial: institute.is_ministerial || false,
    });
    setSelectedInstitute(institute);
    setEditMode(true);
    setShow(true);
  }, []); // No dependencies, as setFormData, setSelectedInstitute, etc., are stable

  // Handle form submission
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      if (editMode && selectedInstitute) {
        // Update institute
        await InstituteService.updateInstitute(selectedInstitute.uuid, formData);
        toastService.success('موسسه با موفقیت به‌روزرسانی شد');
      } else {
        // Add new institute
        await InstituteService.addInstitute(formData);
        toastService.success('موسسه جدید با موفقیت اضافه شد');
      }

      handleClose();
      fetchInstituteData(); // Refresh the list
    } catch (error) {
      console.error('Error saving institute:', error);
      toastService.error(
        error?.response?.data?.data?.[0] || (editMode ? 'خطا در به‌روزرسانی موسسه' : 'خطا در افزودن موسسه')
      );
    } finally {
      setLoading(false);
    }
  };

  // Handle delete institute
  const handleDeleteInstitute = useCallback(
    async (institute) => {
      if (window.confirm('آیا از حذف این موسسه اطمینان دارید؟')) {
        try {
          setLoading(true);
          await InstituteService.deleteInstitute(institute.uuid);
          toastService.success('موسسه با موفقیت حذف شد');
          fetchInstituteData(); // Refresh the list
        } catch (error) {
          console.error('Error deleting institute:', error);
          toastService.error(error?.response?.data?.data?.[0] || 'خطا در حذف موسسه');
        } finally {
          setLoading(false);
        }
      }
    },
    [fetchInstituteData]
  );

  // Handle delete institute member
  const handleDeleteUser = useCallback(
    async (institute) => {
      if (window.confirm('آیا از کاربر حذف این موسسه اطمینان دارید؟')) {
        try {
          setLoading(true);
          await InstituteService.deleteInstituteUser(institute.uuid);
          toastService.success('کاربر موسسه با موفقیت حذف شد');
          fetchInstituteData(); // Refresh the list
        } catch (error) {
          console.error('Error deleting institute member:', error);
          toastService.error(error?.response?.data?.data?.[0] || 'خطا در حذف کاربر موسسه');
        } finally {
          setLoading(false);
        }
      }
    },
    [fetchInstituteData]
  );

  return (
    <Fragment>
      <div className="breadcrumb-header justify-content-between">
        <div className="left-content mt-2">
          <Link className="btn ripple btn-primary" to="#" onClick={handleShow}>
            <i className="fe fe-plus me-2"></i>افزودن موسسه جدید
          </Link>

          <Modal show={show} onHide={handleClose}>
            <Modal.Header className="modal-header">
              <h6 className="modal-title">{editMode ? 'ویرایش موسسه' : 'افزودن موسسه جدید'}</h6>
              <Button variant="" className="btn-close" type="button" onClick={handleClose}>
                <span aria-hidden="true">×</span>
              </Button>
            </Modal.Header>

            <Modal.Body className="modal-body">
              <div className="p-4">
                <Form className="form-horizontal">
                  <FormGroup className="form-group">
                    <Form.Label className="form-label">عنوان *</Form.Label>
                    <Form.Control
                      type="text"
                      className={`form-control ${formErrors.title ? 'is-invalid' : ''}`}
                      id="inputName"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="عنوان را وارد کنید"
                    />
                    {formErrors.title && <div className="invalid-feedback">{formErrors.title}</div>}
                  </FormGroup>
                  <FormGroup className="form-group mb-0 justify-content-end">
                    <div className="checkbox">
                      <div className="custom-checkbox custom-control">
                        <Form.Control
                          type="checkbox"
                          name="has_working_group"
                          checked={formData.has_working_group}
                          onChange={handleInputChange}
                          className="custom-control-input"
                          id="checkbox-1"
                        />
                        <Form.Label htmlFor="checkbox-1" className="custom-control-label mt-1">
                          کارگروه دارد؟
                        </Form.Label>
                      </div>
                    </div>
                  </FormGroup>
                  {/* <FormGroup className="form-group mb-0 justify-content-end">
                    <div className="checkbox">
                      <div className="custom-checkbox custom-control">
                        <Form.Control
                          type="checkbox"
                          name="is_ministerial"
                          checked={formData.is_ministerial}
                          onChange={handleInputChange}
                          className="custom-control-input"
                          id="checkbox-2"
                        />
                        <Form.Label htmlFor="checkbox-2" className="custom-control-label mt-1">
                          آیا کارگروه وزارتی است؟
                        </Form.Label>
                      </div>
                    </div>
                  </FormGroup> */}
                </Form>
              </div>
            </Modal.Body>
            <Modal.Footer>
              <Button
                variant=""
                className="btn ripple btn-primary"
                type="button"
                onClick={handleSubmit}
                disabled={loading}
              >
                {loading ? 'در حال پردازش...' : editMode ? 'به‌روزرسانی' : 'افزودن'}
              </Button>
              <Button variant="" className="btn ripple btn-secondary" onClick={handleClose}>
                بستن
              </Button>
            </Modal.Footer>
          </Modal>
        </div>
      </div>

      <InstituteList
        data={institutesData}
        total={institutesTotal}
        loading={loading}
        onEdit={handleEditInstitute}
        onDelete={handleDeleteInstitute}
        onDeleteUser={handleDeleteUser}
      />
    </Fragment>
  );
};

export default Institute;
