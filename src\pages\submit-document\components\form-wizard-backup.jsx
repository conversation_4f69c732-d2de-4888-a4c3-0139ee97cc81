import { Form, FormGroup } from 'react-bootstrap';
import Select from 'react-select';
import React, { Fragment, useCallback, useState } from 'react';
import useSubmitDocumentFormStore from 'store/submitDocumentFormStore.js';
import BookmarkService from 'service/api/bookmarkService.js';
import toastService from 'utils/toastService.js';
import PaperService from 'service/api/paperService.js';

const Wizard = ({ step: currentIndex, ...props }) => {
  const steps = React.Children.toArray(props.children);
  const prevStep = currentIndex !== 0 && steps[currentIndex - 1].props;
  const nextStep = currentIndex !== steps.length - 1 && steps[currentIndex + 1].props;

  return (
    <div>
      <nav className="btn-group steps basicsteps" style={{ padding: 20 }}>
        {steps.map((step, index) => (
          <Button
            key={step.props.number}
            onClick={() => props.onChange(index)}
            className={getClsNavBtn(index === currentIndex)}
            style={{ fontSize: '0.8rem' }}
          >
            <span className="number me-2 ">{step.props.number}</span>
            <strong>{step.props.title}</strong>
          </Button>
        ))}
      </nav>

      {steps[currentIndex]}

      <div className=" p-3 d-flex justify-content-between  ">
        <Button visible={prevStep} onClick={() => props.onChange(currentIndex - 1)} title={prevStep.description}>
          قبلی
        </Button>
        <Button visible={nextStep} onClick={() => props.onChange(currentIndex + 1)} title={nextStep.description}>
          بعدی
        </Button>
        {!nextStep && (
          <Button visible={!nextStep} onClick={() => props.onSubmit()} title={nextStep.description}>
            ثبت نهایی
          </Button>
        )}
      </div>
    </div>
  );
};
const Step = ({ children }) => children;

function getClsNavBtn(active) {
  return 'btn' + (active ? ' active' : '');
}
function Button({ visible, ...props }) {
  return <button className={visible ? 'btn btn-primary ' : 'invisible'} {...props} />;
}

const FormWizard = () => {
  const {
    step,
    step1,
    step2,
    step3,
    step4,
    step5,
    step6,
    setStep,
    setStep1,
    setStep2,
    setStep3,
    setStep4,
    setStep5,
    setStep6,
  } = useSubmitDocumentFormStore();

  const [validationErrors, setValidationErrors] = useState({});
  const [validatedSteps, setValidatedSteps] = useState([]);
  const [loading, setLoading] = useState(false);

  const educationLevelOptions = [
    { value: '', label: 'انتخاب کنید' },
    { value: 'phd', label: 'دکترای تخصصی (PHD)' },
    { value: 'professional_doctorate', label: 'دکترای حرفه‌ای' },
    { value: 'master', label: 'کارشناسی ارشد' },
    { value: 'bachelor', label: 'کارشناسی' },
    { value: 'associate', label: 'کاردانی' },
    { value: 'clinical_specialty', label: 'تخصص / فوق تخصص بالینی' },
  ];

  // University options (placeholder, replace with actual list)
  const universityOptions = [
    { value: '', label: 'انتخاب کنید' },
    { value: 'uni1', label: 'دانشگاه ۱' },
    { value: 'uni2', label: 'دانشگاه ۲' },
    { value: 'uni3', label: 'دانشگاه ۳' },
    { value: 'other', label: 'سایر' },
  ];

  const validateStep = useCallback(
    (currentStep) => {
      const errors = {};
      let isValid = true;

      switch (currentStep) {
        case 0: // Step 1: کارگروه دریافت کننده گزارش
          if (!step1.organ) {
            errors.organ = 'لطفاً کارگروه را انتخاب کنید';
            isValid = false;
          }
          if (step1.organ === 'other' && !step1.customOrgan) {
            errors.customOrgan = 'لطفاً نام کارگروه را وارد کنید';
            isValid = false;
          }
          break;

        case 1: // Step 2: گزارش دهنده
          if (!step2.reporterFullname) {
            errors.reporterFullname = 'لطفاً نام و نام خانوادگی را وارد کنید';
            isValid = false;
          }
          if (!step2.reporterGender) {
            errors.reporterGender = 'لطفاً جنسیت را انتخاب کنید';
            isValid = false;
          }
          if (!step2.reporterNationalID) {
            errors.reporterNationalID = 'لطفاً کد ملی را وارد کنید';
            isValid = false;
          }
          if (!step2.reporterPhone) {
            errors.reporterPhone = 'لطفاً شماره تماس را وارد کنید';
            isValid = false;
          }
          if (!step2.reporterEmail) {
            errors.reporterEmail = 'لطفاً رایانامه را وارد کنید';
            isValid = false;
          }
          if (!step2.reporterOrgan) {
            errors.reporterOrgan = 'لطفاً موسسه محل خدمت را انتخاب کنید';
            isValid = false;
          }
          if (step2.reporterOrgan === 'other' && !step2.reporterCustomOrgan) {
            errors.reporterCustomOrgan = 'لطفاً نام موسسه را وارد کنید';
            isValid = false;
          }
          if (!step2.reporterRole) {
            errors.reporterRole = 'لطفاً نقش حرفه‌ای و آکادمیک را انتخاب کنید';
            isValid = false;
          }
          if (step2.reporterRole === 'faculty_member') {
            if (!step2.reporterGrade) {
              errors.reporterGrade = 'لطفاً مرتبه علمی را وارد کنید';
              isValid = false;
            }
            if (!step2.reporterField) {
              errors.reporterField = 'لطفاً رشته تحصیلی را وارد کنید';
              isValid = false;
            }
            if (!step2.reporterYearsOfService) {
              errors.reporterYearsOfService = 'لطفاً سنوات خدمت را وارد کنید';
              isValid = false;
            }
            if (!step2.reporterEducationLevel) {
              errors.reporterEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          if (step2.reporterRole === 'student') {
            if (!step2.reporterField) {
              errors.reporterField = 'لطفاً رشته تحصیلی را وارد کنید';
              isValid = false;
            }
            if (!step2.reporterEducationLevel) {
              errors.reporterEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
            if (!step2.reporterAcademicStatus) {
              errors.reporterAcademicStatus = 'لطفاً وضعیت تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          if (step2.reporterRole === 'non_faculty_member') {
            if (!step2.reporterAdministrativePosition) {
              errors.reporterAdministrativePosition = 'لطفاً سمت اجرایی را وارد کنید';
              isValid = false;
            }
            if (!step2.reporterEducationLevel) {
              errors.reporterEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          if (step2.reporterRole === 'independent_researcher') {
            if (!step2.reporterEducationLevel) {
              errors.reporterEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          break;

        case 2: // Step 3: گزارش شونده
          if (!step3.reportedFullname) {
            errors.reportedFullname = 'لطفاً نام و نام خانوادگی را وارد کنید';
            isValid = false;
          }
          if (!step3.reportedGender) {
            errors.reportedGender = 'لطفاً جنسیت را انتخاب کنید';
            isValid = false;
          }
          if (!step3.reportedNationalID) {
            errors.reportedNationalID = 'لطفاً کد ملی را وارد کنید';
            isValid = false;
          }
          if (!step3.reportedPhone) {
            errors.reportedPhone = 'لطفاً شماره تماس را وارد کنید';
            isValid = false;
          }
          if (!step3.reportedEmail) {
            errors.reportedEmail = 'لطفاً رایانامه را وارد کنید';
            isValid = false;
          }
          if (!step3.reportedOrgan) {
            errors.reportedOrgan = 'لطفاً موسسه محل خدمت را انتخاب کنید';
            isValid = false;
          }
          if (step3.reportedOrgan === 'other' && !step3.reportedCustomOrgan) {
            errors.reportedCustomOrgan = 'لطفاً نام موسسه را وارد کنید';
            isValid = false;
          }
          if (!step3.reportedRole) {
            errors.reportedRole = 'لطفاً نقش حرفه‌ای و آکادمیک را انتخاب کنید';
            isValid = false;
          }
          if (step3.reportedRole === 'faculty_member') {
            if (!step3.reportedGrade) {
              errors.reportedGrade = 'لطفاً مرتبه علمی را وارد کنید';
              isValid = false;
            }
            if (!step3.reportedField) {
              errors.reportedField = 'لطفاً رشته تحصیلی را وارد کنید';
              isValid = false;
            }
            if (!step3.reportedYearsOfService) {
              errors.reportedYearsOfService = 'لطفاً سنوات خدمت را وارد کنید';
              isValid = false;
            }
            if (!step3.reportedEducationLevel) {
              errors.reportedEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          if (step3.reportedRole === 'student') {
            if (!step3.reportedField) {
              errors.reportedField = 'لطفاً رشته تحصیلی را وارد کنید';
              isValid = false;
            }
            if (!step3.reportedEducationLevel) {
              errors.reportedEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
            if (!step3.reportedAcademicStatus) {
              errors.reportedAcademicStatus = 'لطفاً وضعیت تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          if (step3.reportedRole === 'non_faculty_member') {
            if (!step3.reportedAdministrativePosition) {
              errors.reportedAdministrativePosition = 'لطفاً سمت اجرایی را وارد کنید';
              isValid = false;
            }
            if (!step3.reportedEducationLevel) {
              errors.reportedEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          if (
            [
              'independent_researcher',
              'institute_president',
              'institute_vice_president',
              'board_member',
              'trustee_member',
              'ethics_committee_member',
            ].includes(step3.reportedRole)
          ) {
            if (!step3.reportedEducationLevel) {
              errors.reportedEducationLevel = 'لطفاً مقطع تحصیلی را انتخاب کنید';
              isValid = false;
            }
          }
          if (!step3.reportedHasViolation) {
            errors.reportedHasViolation = 'لطفاً سابقه تخلف پژوهشی را مشخص کنید';
            isValid = false;
          }
          if (step3.reportedHasViolation === 'yes') {
            if (!step3.reportedRepeatViolation) {
              errors.reportedRepeatViolation = 'لطفاً تکرار تخلفات قبلی را مشخص کنید';
              isValid = false;
            }
            if (!step3.reportedViolationTypes) {
              errors.reportedViolationTypes = 'لطفاً انواع تخلفات را وارد کنید';
              isValid = false;
            }
            if (!step3.reportedMultiInstituteViolations) {
              errors.reportedMultiInstituteViolations = 'لطفاً مشخص کنید تخلفات در چند موسسه بوده‌اند';
              isValid = false;
            }
            if (!step3.reportedViolationCases) {
              errors.reportedViolationCases = 'لطفاً شماره پرونده‌های تخلفات قبلی را وارد کنید';
              isValid = false;
            }
          }
          if (!step3.reportedViolationMultipleInstitutes) {
            errors.reportedViolationMultipleInstitutes = 'لطفاً مشخص کنید تخلف در چند موسسه واقع شده است';
            isValid = false;
          }
          step3.reportedViolationInstitutes.forEach((institute, index) => {
            if (!institute.university) {
              errors[`institute_university_${index}`] = 'لطفاً نام دانشگاه را انتخاب کنید';
              isValid = false;
            }
            if (institute.university === 'other' && !institute.customUniversity) {
              errors[`institute_customUniversity_${index}`] = 'لطفاً نام موسسه را وارد کنید';
              isValid = false;
            }
            if (!institute.universityType) {
              errors[`institute_universityType_${index}`] = 'لطفاً سطح تیپ دانشگاه را انتخاب کنید';
              isValid = false;
            }
            if (!institute.instituteType) {
              errors[`institute_instituteType_${index}`] = 'لطفاً نوع موسسه را انتخاب کنید';
              isValid = false;
            }
            if (!institute.location) {
              errors[`institute_location_${index}`] = 'لطفاً محل جغرافیایی موسسه را مشخص کنید';
              isValid = false;
            }
            if (!institute.hasEthicsCommittee) {
              errors[`institute_hasEthicsCommittee_${index}`] = 'لطفاً مشخص کنید موسسه دارای کارگروه اخلاق است';
              isValid = false;
            }
            if (!institute.reportedRelation) {
              errors[`institute_reportedRelation_${index}`] = 'لطفاً رابطه سازمانی گزارش‌شونده را انتخاب کنید';
              isValid = false;
            }
            if (!institute.reporterRelation) {
              errors[`institute_reporterRelation_${index}`] = 'لطفاً رابطه سازمانی گزارش‌دهنده را انتخاب کنید';
              isValid = false;
            }
          });
          break;

        case 3: // Step 4: نحوه گزارش تخلف
          if (!step4.reportReceivedDate) {
            errors.reportReceivedDate = 'لطفاً تاریخ دریافت گزارش را وارد کنید';
            isValid = false;
          }
          if (!step4.reportDelay) {
            errors.reportDelay = 'لطفاً مدت زمان گزارش را انتخاب کنید';
            isValid = false;
          }
          if (!step4.reportMethod) {
            errors.reportMethod = 'لطفاً نحوه اعلام گزارش را مشخص کنید';
            isValid = false;
          }
          if (step4.reportMethod === 'others_whistleblowing' && !step4.whistleblowerSource) {
            errors.whistleblowerSource = 'لطفاً منبع افشاگری را انتخاب کنید';
            isValid = false;
          }
          if (!step4.reporterIdentity) {
            errors.reporterIdentity = 'لطفاً هویت گزارش‌دهنده را مشخص کنید';
            isValid = false;
          }
          if (!step4.reporterViolationRole) {
            errors.reporterViolationRole = 'لطفاً نقش گزارش‌دهنده را مشخص کنید';
            isValid = false;
          }
          if (!step4.reporterStakeholder) {
            errors.reporterStakeholder = 'لطفاً ذینفعی گزارش‌دهنده را مشخص کنید';
            isValid = false;
          }
          if (!step4.reporterReportedRelation) {
            errors.reporterReportedRelation = 'لطفاً رابطه گزارش‌دهنده با گزارش‌شونده را انتخاب کنید';
            isValid = false;
          }
          if (!step4.reportRecipient) {
            errors.reportRecipient = 'لطفاً مرجع دریافت‌کننده گزارش را انتخاب کنید';
            isValid = false;
          }
          if (!step4.reportFormat) {
            errors.reportFormat = 'لطفاً شکل گزارش تخلف را مشخص کنید';
            isValid = false;
          }
          if (!step4.reportChannel) {
            errors.reportChannel = 'لطفاً مسیر ارسال گزارش را انتخاب کنید';
            isValid = false;
          }
          if (!step4.researchStage) {
            errors.researchStage = 'لطفاً مرحله پژوهش را انتخاب کنید';
            isValid = false;
          }
          break;

        case 4: // Step 5: شرح تخلف توسط گزارش‌دهنده
          if (!step5.violationDescription) {
            errors.violationDescription = 'لطفاً شرح تخلف را وارد کنید';
            isValid = false;
          }
          if (!step5.violationDocuments || step5.violationDocuments.length === 0) {
            errors.violationDocuments = 'لطفاً حداقل یک مستند ارائه کنید';
            isValid = false;
          }
          break;

        case 5: // Step 6: شرح تخلف توسط گزارش‌شونده
          if (!step6.reportedDefenseDescription) {
            errors.reportedDefenseDescription = 'لطفاً توضیحات و دفاعیات را وارد کنید';
            isValid = false;
          }
          if (!step6.reportedDefenseDocuments || step6.reportedDefenseDocuments.length === 0) {
            errors.reportedDefenseDocuments = 'لطفاً حداقل یک مستند ارائه کنید';
            isValid = false;
          }
          break;

        default:
          isValid = false;
      }

      setValidationErrors(errors);
      return isValid;
    },
    [step1, step2, step3, step4, step5, step6]
  );

  const handleStep = (nextStep) => {
    if (nextStep === step) return;
    if (validatedSteps.includes(nextStep)) {
      setStep(nextStep);
    } else {
      if (validateStep(step)) {
        setValidatedSteps([...validatedSteps, step]);
        setStep(nextStep);
      }
    }
  };

  const handleSubmit = async () => {
    try {
      if (loading) return;
      setLoading(true);
      await PaperService.addPaper({ params: { step1, step2, step3, step4, step5, step6 } });
      toastService.success('پرونده جدید با موفقیت ثبت شد!');
      // setShowNoteBullet(true);
    } catch (e) {
      console.log(e);
      toastService.error('خطا در عملیات!');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Wizard step={step} onChange={handleStep} onSubmit={handleSubmit}>
      <Step title="کارگروه دریافت کننده گزارش" number="۱">
        <section className="card-body Basicwizard ">
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">انتخاب کارگروه</Form.Label>
            <Select
              options={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'a', label: 'کارگروه ۱' },
              ]}
              placeholder="کارگروه خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'a', label: 'کارگروه ۱' },
              ].find((option) => option.value === step1.organ)}
              onChange={(e) =>
                setStep1({
                  organ: e.value,
                  customOrgan: e.value === 'other' ? '' : step1.customOrgan,
                })
              }
            />
            <div className="invalid-feedback" style={{ display: validationErrors.organ ? 'block' : 'none' }}>
              {validationErrors.organ}
            </div>
          </Form.Group>
          {step1.organ === 'other' && (
            <Form.Group className="control-group form-group">
              <Form.Label className="form-label">نام کارگروه</Form.Label>
              <Form.Control
                id={'organ-name'}
                type="text"
                className="form-control"
                required
                placeholder="نام کارگروه"
                value={step1.customOrgan}
                onChange={(e) =>
                  setStep1({
                    customOrgan: e.target.value,
                  })
                }
              />

              <div className="invalid-feedback" style={{ display: validationErrors.customOrgan ? 'block' : 'none' }}>
                {validationErrors.customOrgan}
              </div>
            </Form.Group>
          )}
        </section>
      </Step>
      <Step title="گزارش دهنده" number="۲">
        <section className="card-body Basicwizard ">
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">نام و نام خانوادگی</Form.Label>
            <Form.Control
              id={'reporter-fullname'}
              type="text"
              size="sm"
              className="form-control"
              required
              placeholder="نام و نام خانوادگی را وارد کنید"
              value={step2.reporterFullname}
              onChange={(e) =>
                setStep2({
                  reporterFullname: e.target.value,
                })
              }
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reporterFullname ? 'block' : 'none' }}>
              {validationErrors.reporterFullname}
            </div>
          </Form.Group>
          <FormGroup className="form-group">
            <Form.Label className="form-label">جنسیت</Form.Label>
            <div className="d-flex flex-row align-items-center gap-4">
              {/* Option for "مجرد" */}
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="marital-status"
                  value="مرد"
                  checked={step2.reporterGender === 'male'}
                  onChange={() => {
                    setStep2({ reporterGender: 'male' });
                  }}
                />
                <span className="custom-control-label">مرد</span>
              </Form.Label>

              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="marital-status"
                  value="زن"
                  checked={step2.reporterGender === 'female'}
                  onChange={() => {
                    setStep2({ reporterGender: 'female' });
                  }}
                />
                <span className="custom-control-label">زن</span>
              </Form.Label>
            </div>
            <div className="invalid-feedback" style={{ display: validationErrors.reporterGender ? 'block' : 'none' }}>
              {validationErrors.reporterGender}
            </div>
          </FormGroup>
          <FormGroup className="form-group">
            <Form.Label htmlFor="reporter-nationalID">کد ملی</Form.Label>
            <Form.Control
              id={'reporter-nationalID'}
              type="text"
              size="sm"
              className="form-control"
              placeholder="کد ملی را وارد کنید"
              value={step2.reporterNationalID}
              onChange={(e) =>
                setStep2({
                  reporterNationalID: e.target.value,
                })
              }
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reporterNationalID ? 'block' : 'none' }}
            >
              {validationErrors.reporterNationalID}
            </div>
          </FormGroup>
          <FormGroup className="form-group">
            <Form.Label>شماره تماس</Form.Label>
            <Form.Control
              id={'reporter-phone'}
              type="text"
              size="sm"
              className="form-control"
              placeholder="شماره تماس را وارد کنید"
              value={step2.reporterPhone}
              onChange={(e) =>
                setStep2({
                  reporterPhone: e.target.value,
                })
              }
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reporterPhone ? 'block' : 'none' }}>
              {validationErrors.reporterPhone}
            </div>
          </FormGroup>
          <FormGroup className="form-group">
            <Form.Label>رایانامه</Form.Label>
            <Form.Control
              id={'reporter-email'}
              type="text"
              size="sm"
              className="form-control"
              placeholder="رایانامه خود را وارد کنید"
              value={step2.reporterEmail}
              onChange={(e) =>
                setStep2({
                  reporterEmail: e.target.value,
                })
              }
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reporterEmail ? 'block' : 'none' }}>
              {validationErrors.reporterEmail}
            </div>
          </FormGroup>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">موسسه محل خدمت</Form.Label>
            <Select
              options={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'a', label: 'موسسه ۱' },
                { value: 'b', label: 'موسسه ۲' },
                { value: 'c', label: 'موسسه ۳' },
                { value: 'd', label: '...' },
                { value: 'other', label: 'سایر' },
              ]}
              placeholder="موسسه خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'a', label: 'موسسه ۱' },
                { value: 'b', label: 'موسسه ۲' },
                { value: 'c', label: 'موسسه ۳' },
                { value: 'd', label: '...' },
                { value: 'other', label: 'سایر' },
              ].find((option) => option.value === step2.reporterOrgan)}
              onChange={(e) =>
                setStep2({
                  reporterOrgan: e.value,
                  reporterCustomOrgan: e.value === 'other' ? '' : step2.reporterCustomOrgan,
                })
              }
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reporterOrgan ? 'block' : 'none' }}>
              {validationErrors.reporterOrgan}
            </div>
          </Form.Group>
          {step2.reporterOrgan === 'other' && (
            <Form.Group className="control-group form-group">
              <Form.Label className="form-label">نام موسسه</Form.Label>
              <Form.Control
                id={'reporter-organ-name'}
                type="text"
                className="form-control"
                required
                placeholder="نام موسسه"
                value={step2.reporterCustomOrgan}
                onChange={(e) =>
                  setStep2({
                    reporterCustomOrgan: e.target.value,
                  })
                }
              />
              <div
                className="invalid-feedback"
                style={{ display: validationErrors.reporterCustomOrgan ? 'block' : 'none' }}
              >
                {validationErrors.reporterCustomOrgan}
              </div>
            </Form.Group>
          )}

          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">نقش حرفه‌ای و آکادمیک</Form.Label>
            <Select
              options={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'faculty_member', label: 'عضو هیئت علمی' },
                { value: 'student', label: 'دانشجو' },
                { value: 'non_faculty_member', label: 'اعضای غیر هیئت علمی' },
                { value: 'independent_researcher', label: 'پژوهشگر آزاد' },
              ]}
              placeholder="نقش آکادمیک خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'faculty_member', label: 'عضو هیئت علمی' },
                { value: 'student', label: 'دانشجو' },
                { value: 'non_faculty_member', label: 'اعضای غیر هیئت علمی' },
                { value: 'independent_researcher', label: 'پژوهشگر آزاد' },
              ].find((option) => option.value === step2.reporterRole)}
              onChange={(e) =>
                setStep2({
                  reporterRole: e.value,
                  reporterGrade: '',
                  reporterField: '',
                  reporterYearsOfService: '',
                  reporterEducationLevel: '',
                  reporterAcademicStatus: '',
                  reporterAdministrativePosition: '',
                })
              }
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reporterRole ? 'block' : 'none' }}>
              {validationErrors.reporterRole}
            </div>
          </Form.Group>
          {step2.reporterRole === 'faculty_member' && (
            <Fragment>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">مرتبه علمی</Form.Label>
                <Form.Control
                  id="reporter-faculty-grade"
                  type="text"
                  className="form-control"
                  required
                  placeholder="مرتبه علمی خود را وارد کنید"
                  value={step2.reporterGrade || ''}
                  onChange={(e) =>
                    setStep2({
                      reporterGrade: e.target.value,
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reporterGrade ? 'block' : 'none' }}
                >
                  {validationErrors.reporterGrade}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">رشته تحصیلی</Form.Label>
                <Form.Control
                  id="reporter-faculty-field"
                  type="text"
                  className="form-control"
                  required
                  placeholder="رشته تحصیلی خود را وارد کنید"
                  value={step2.reporterField || ''}
                  onChange={(e) =>
                    setStep2({
                      reporterField: e.target.value,
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reporterField ? 'block' : 'none' }}
                >
                  {validationErrors.reporterField}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">سنوات خدمت</Form.Label>
                <Form.Control
                  id="reporter-faculty-years-of-service"
                  type="text"
                  className="form-control"
                  required
                  placeholder="سنوات خدمت خود را وارد کنید"
                  value={step2.reporterYearsOfService || ''}
                  onChange={(e) =>
                    setStep2({
                      reporterYearsOfService: e.target.value,
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reporterYearsOfService ? 'block' : 'none' }}
                >
                  {validationErrors.reporterYearsOfService}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
                <Select
                  options={educationLevelOptions}
                  placeholder="مقطع تحصیلی خود را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  value={educationLevelOptions.find((option) => option.value === step2.reporterEducationLevel)}
                  onChange={(e) =>
                    setStep2({
                      reporterEducationLevel: e.value,
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reporterEducationLevel ? 'block' : 'none' }}
                >
                  {validationErrors.reporterEducationLevel}
                </div>
              </Form.Group>
            </Fragment>
          )}
          {step2.reporterRole === 'student' && (
            <Fragment>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">رشته تحصیلی</Form.Label>
                <Form.Control
                  id="reporter-student-field"
                  type="text"
                  className="form-control"
                  required
                  placeholder="رشته تحصیلی خود را وارد کنید"
                  value={step2.reporterField || ''}
                  onChange={(e) =>
                    setStep2({
                      reporterField: e.target.value,
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reporterField ? 'block' : 'none' }}
                >
                  {validationErrors.reporterField}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
                <Select
                  options={educationLevelOptions}
                  placeholder="مقطع تحصیلی خود را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  value={educationLevelOptions.find((option) => option.value === step2.reporterEducationLevel)}
                  onChange={(e) =>
                    setStep2({
                      reporterEducationLevel: e.value,
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reporterEducationLevel ? 'block' : 'none' }}
                >
                  {validationErrors.reporterEducationLevel}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">وضعیت تحصیلی</Form.Label>
                <Select
                  options={[
                    { value: '', label: 'انتخاب کنید' },
                    { value: 'graduated', label: 'فارغ‌التحصیل' },
                    { value: 'studying', label: 'شاغل به تحصیل' },
                  ]}
                  value={[
                    { value: '', label: 'انتخاب کنید' },
                    { value: 'graduated', label: 'فارغ‌التحصیل' },
                    { value: 'studying', label: 'شاغل به تحصیل' },
                  ].find((option) => option.value === step2.reporterAcademicStatus)}
                  placeholder="وضعیت تحصیلی خود را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  onChange={(e) =>
                    setStep2({
                      reporterAcademicStatus: e.value,
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reporterAcademicStatus ? 'block' : 'none' }}
                >
                  {validationErrors.reporterAcademicStatus}
                </div>
              </Form.Group>
            </Fragment>
          )}

          {/* Fragment for Non-Faculty Member */}
          {step2.reporterRole === 'non_faculty_member' && (
            <Fragment>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">سمت اجرایی</Form.Label>
                <Form.Control
                  id="reporter-non-faculty-admin-position"
                  type="text"
                  className="form-control"
                  required
                  placeholder="سمت اجرایی خود را وارد کنید"
                  value={step2.reporterAdministrativePosition || ''}
                  onChange={(e) =>
                    setStep2({
                      reporterAdministrativePosition: e.target.value,
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reporterAdministrativePosition ? 'block' : 'none' }}
                >
                  {validationErrors.reporterAdministrativePosition}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
                <Select
                  options={educationLevelOptions}
                  placeholder="مقطع تحصیلی خود را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  value={educationLevelOptions.find((option) => option.value === step2.reporterEducationLevel)}
                  onChange={(e) =>
                    setStep2({
                      reporterEducationLevel: e.value,
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reporterEducationLevel ? 'block' : 'none' }}
                >
                  {validationErrors.reporterEducationLevel}
                </div>
              </Form.Group>
            </Fragment>
          )}

          {/* Fragment for Independent Researcher */}
          {step2.reporterRole === 'independent_researcher' && (
            <Fragment>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
                <Select
                  options={educationLevelOptions}
                  placeholder="مقطع تحصیلی خود را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  value={educationLevelOptions.find((option) => option.value === step2.reporterEducationLevel)}
                  onChange={(e) =>
                    setStep2({
                      reporterEducationLevel: e.value,
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reporterEducationLevel ? 'block' : 'none' }}
                >
                  {validationErrors.reporterEducationLevel}
                </div>
              </Form.Group>
            </Fragment>
          )}
        </section>
      </Step>
      <Step title="گزارش شونده" number="۳">
        <section className="card-body Basicwizard">
          {/* مشخصات */}
          <h5>مشخصات</h5>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">نام و نام خانوادگی</Form.Label>
            <Form.Control
              id="reported-fullname"
              type="text"
              size="sm"
              className="form-control"
              required
              placeholder="نام و نام خانوادگی را وارد کنید"
              value={step3.reportedFullname || ''}
              onChange={(e) => setStep3({ reportedFullname: e.target.value })}
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reportedFullname ? 'block' : 'none' }}>
              {validationErrors.reportedFullname}
            </div>
          </Form.Group>

          <Form.Group className="form-group">
            <Form.Label className="form-label">جنسیت</Form.Label>
            <div className="d-flex flex-row align-items-center gap-4">
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reported-gender"
                  value="male"
                  checked={step3.reportedGender === 'male'}
                  onChange={() => setStep3({ reportedGender: 'male' })}
                />
                <span className="custom-control-label">مرد</span>
              </Form.Label>
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reported-gender"
                  value="female"
                  checked={step3.reportedGender === 'female'}
                  onChange={() => setStep3({ reportedGender: 'female' })}
                />
                <span className="custom-control-label">زن</span>
              </Form.Label>
            </div>
            <div className="invalid-feedback" style={{ display: validationErrors.reportedGender ? 'block' : 'none' }}>
              {validationErrors.reportedGender}
            </div>
          </Form.Group>

          <Form.Group className="form-group">
            <Form.Label htmlFor="reported-nationalID">کد ملی</Form.Label>
            <Form.Control
              id="reported-nationalID"
              type="text"
              size="sm"
              className="form-control"
              placeholder="کد ملی را وارد کنید"
              value={step3.reportedNationalID || ''}
              onChange={(e) => setStep3({ reportedNationalID: e.target.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedNationalID ? 'block' : 'none' }}
            >
              {validationErrors.reportedNationalID}
            </div>
          </Form.Group>

          <Form.Group className="form-group">
            <Form.Label>شماره تماس</Form.Label>
            <Form.Control
              id="reported-phone"
              type="text"
              size="sm"
              className="form-control"
              placeholder="شماره تماس را وارد کنید"
              value={step3.reportedPhone || ''}
              onChange={(e) => setStep3({ reportedPhone: e.target.value })}
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reportedPhone ? 'block' : 'none' }}>
              {validationErrors.reportedPhone}
            </div>
          </Form.Group>

          <Form.Group className="form-group">
            <Form.Label>رایانامه</Form.Label>
            <Form.Control
              id="reported-email"
              type="text"
              size="sm"
              className="form-control"
              placeholder="رایانامه خود را وارد کنید"
              value={step3.reportedEmail || ''}
              onChange={(e) => setStep3({ reportedEmail: e.target.value })}
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reportedEmail ? 'block' : 'none' }}>
              {validationErrors.reportedEmail}
            </div>
          </Form.Group>

          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">موسسه محل خدمت</Form.Label>
            <Select
              options={universityOptions}
              placeholder="موسسه خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={universityOptions.find((option) => option.value === step3.reportedOrgan)}
              onChange={(e) =>
                setStep3({
                  reportedOrgan: e.value,
                  reportedCustomOrgan: e.value === 'other' ? '' : step3.reportedCustomOrgan,
                })
              }
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reportedOrgan ? 'block' : 'none' }}>
              {validationErrors.reportedOrgan}
            </div>
          </Form.Group>
          {step3.reportedOrgan === 'other' && (
            <Form.Group className="control-group form-group">
              <Form.Label className="form-label">نام موسسه</Form.Label>
              <Form.Control
                id="reported-organ-name"
                type="text"
                className="form-control"
                required
                placeholder="نام موسسه"
                value={step3.reportedCustomOrgan || ''}
                onChange={(e) => setStep3({ reportedCustomOrgan: e.target.value })}
              />

              <div
                className="invalid-feedback"
                style={{ display: validationErrors.reportedCustomOrgan ? 'block' : 'none' }}
              >
                {validationErrors.reportedCustomOrgan}
              </div>
            </Form.Group>
          )}

          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">نقش حرفه‌ای و آکادمیک</Form.Label>
            <Select
              options={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'faculty_member', label: 'عضو هیئت علمی' },
                { value: 'student', label: 'دانشجو' },
                { value: 'non_faculty_member', label: 'اعضای غیر هیئت علمی' },
                { value: 'independent_researcher', label: 'پژوهشگر آزاد' },
                { value: 'institute_president', label: 'روسای موسسه' },
                { value: 'institute_vice_president', label: 'معاونان کل موسسه' },
                { value: 'board_member', label: 'اعضای هیئت رئیسه موسسه' },
                { value: 'trustee_member', label: 'اعضای هیئت امنای موسسه' },
                { value: 'ethics_committee_member', label: 'اعضای کارگروه‌های اخلاق در پژوهش مؤسسه‌ها' },
              ]}
              placeholder="نقش آکادمیک خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'faculty_member', label: 'عضو هیئت علمی' },
                { value: 'student', label: 'دانشجو' },
                { value: 'non_faculty_member', label: 'اعضای غیر هیئت علمی' },
                { value: 'independent_researcher', label: 'پژوهشگر آزاد' },
                { value: 'institute_president', label: 'روسای موسسه' },
                { value: 'institute_vice_president', label: 'معاونان کل موسسه' },
                { value: 'board_member', label: 'اعضای هیئت رئیسه موسسه' },
                { value: 'trustee_member', label: 'اعضای هیئت امنای موسسه' },
                { value: 'ethics_committee_member', label: 'اعضای کارگروه‌های اخلاق در پژوهش مؤسسه‌ها' },
              ].find((option) => option.value === step3.reportedRole)}
              onChange={(e) =>
                setStep3({
                  reportedRole: e.value,
                  reportedGrade: '',
                  reportedField: '',
                  reportedYearsOfService: '',
                  reportedEducationLevel: '',
                  reportedAcademicStatus: '',
                  reportedAdministrativePosition: '',
                })
              }
            />

            <div className="invalid-feedback" style={{ display: validationErrors.reportedRole ? 'block' : 'none' }}>
              {validationErrors.reportedRole}
            </div>
          </Form.Group>

          {step3.reportedRole === 'faculty_member' && (
            <Fragment>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">مرتبه علمی</Form.Label>
                <Form.Control
                  id="reported-faculty-grade"
                  type="text"
                  className="form-control"
                  required
                  placeholder="مرتبه علمی خود را وارد کنید"
                  value={step3.reportedGrade || ''}
                  onChange={(e) => setStep3({ reportedGrade: e.target.value })}
                />

                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedGrade ? 'block' : 'none' }}
                >
                  {validationErrors.reportedGrade}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">رشته تحصیلی</Form.Label>
                <Form.Control
                  id="reported-faculty-field"
                  type="text"
                  className="form-control"
                  required
                  placeholder="رشته تحصیلی خود را وارد کنید"
                  value={step3.reportedField || ''}
                  onChange={(e) => setStep3({ reportedField: e.target.value })}
                />

                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedField ? 'block' : 'none' }}
                >
                  {validationErrors.reportedField}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">سنوات خدمت</Form.Label>
                <Form.Control
                  id="reported-faculty-years-of-service"
                  type="text"
                  className="form-control"
                  required
                  placeholder="سنوات خدمت خود را وارد کنید"
                  value={step3.reportedYearsOfService || ''}
                  onChange={(e) => setStep3({ reportedYearsOfService: e.target.value })}
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedYearsOfService ? 'block' : 'none' }}
                >
                  {validationErrors.reportedYearsOfService}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
                <Select
                  options={educationLevelOptions}
                  placeholder="مقطع تحصیلی خود را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  value={educationLevelOptions.find((option) => option.value === step3.reportedEducationLevel)}
                  onChange={(e) => setStep3({ reportedEducationLevel: e.value })}
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedEducationLevel ? 'block' : 'none' }}
                >
                  {validationErrors.reportedEducationLevel}
                </div>
              </Form.Group>
            </Fragment>
          )}

          {step3.reportedRole === 'student' && (
            <Fragment>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">رشته تحصیلی</Form.Label>
                <Form.Control
                  id="reported-student-field"
                  type="text"
                  className="form-control"
                  required
                  placeholder="رشته تحصیلی خود را وارد کنید"
                  value={step3.reportedField || ''}
                  onChange={(e) => setStep3({ reportedField: e.target.value })}
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedField ? 'block' : 'none' }}
                >
                  {validationErrors.reportedField}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
                <Select
                  options={educationLevelOptions}
                  placeholder="مقطع تحصیلی خود را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  value={educationLevelOptions.find((option) => option.value === step3.reportedEducationLevel)}
                  onChange={(e) => setStep3({ reportedEducationLevel: e.value })}
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedEducationLevel ? 'block' : 'none' }}
                >
                  {validationErrors.reportedEducationLevel}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">وضعیت تحصیلی</Form.Label>
                <Select
                  options={[
                    { value: '', label: 'انتخاب کنید' },
                    { value: 'graduated', label: 'فارغ‌التحصیل' },
                    { value: 'studying', label: 'شاغل به تحصیل' },
                  ]}
                  value={[
                    { value: '', label: 'انتخاب کنید' },
                    { value: 'graduated', label: 'فارغ‌التحصیل' },
                    { value: 'studying', label: 'شاغل به تحصیل' },
                  ].find((option) => option.value === step3.reportedAcademicStatus)}
                  placeholder="وضعیت تحصیلی خود را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  onChange={(e) => setStep3({ reportedAcademicStatus: e.value })}
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedAcademicStatus ? 'block' : 'none' }}
                >
                  {validationErrors.reportedAcademicStatus}
                </div>
              </Form.Group>
            </Fragment>
          )}

          {step3.reportedRole === 'non_faculty_member' && (
            <Fragment>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">سمت اجرایی</Form.Label>
                <Form.Control
                  id="reported-non-faculty-admin-position"
                  type="text"
                  className="form-control"
                  required
                  placeholder="سمت اجرایی خود را وارد کنید"
                  value={step3.reportedAdministrativePosition || ''}
                  onChange={(e) => setStep3({ reportedAdministrativePosition: e.value })}
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedAdministrativePosition ? 'block' : 'none' }}
                >
                  {validationErrors.reportedAdministrativePosition}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
                <Select
                  options={educationLevelOptions}
                  placeholder="مقطع تحصیلی خود را انتخاب کنید"
                  classNamePrefix="Select2"
                  value={educationLevelOptions.find((option) => option.value === step3.reportedEducationLevel)}
                  isSearchable
                  onChange={(e) => setStep3({ reportedEducationLevel: e.value })}
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedEducationLevel ? 'block' : 'none' }}
                >
                  {validationErrors.reportedEducationLevel}
                </div>
              </Form.Group>
            </Fragment>
          )}

          {step3.reportedRole === 'independent_researcher' && (
            <Fragment>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
                <Select
                  options={educationLevelOptions}
                  placeholder="مقطع تحصیلی خود را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  value={educationLevelOptions.find((option) => option.value === step3.reportedEducationLevel)}
                  onChange={(e) => setStep3({ reportedEducationLevel: e.value })}
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedEducationLevel ? 'block' : 'none' }}
                >
                  {validationErrors.reportedEducationLevel}
                </div>
              </Form.Group>
            </Fragment>
          )}

          {/* Other roles (institute_president, institute_vice_president, board_member, trustee_member, ethics_committee_member) */}
          {[
            'institute_president',
            'institute_vice_president',
            'board_member',
            'trustee_member',
            'ethics_committee_member',
          ].includes(step3.reportedRole) && (
            <Fragment>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
                <Select
                  options={educationLevelOptions}
                  placeholder="مقطع تحصیلی خود را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  value={educationLevelOptions.find((option) => option.value === step3.reportedEducationLevel)}
                  onChange={(e) => setStep3({ reportedEducationLevel: e.value })}
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedEducationLevel ? 'block' : 'none' }}
                >
                  {validationErrors.reportedEducationLevel}
                </div>
              </Form.Group>
            </Fragment>
          )}

          {/* سابقه تخلف پژوهشی */}
          <h5 className={'mt-5'}>سابقه تخلف پژوهشی</h5>
          <Form.Group className="form-group">
            <Form.Label className="form-label">سابقه تخلف پژوهشی دارد</Form.Label>
            <div className="d-flex flex-row align-items-center gap-4">
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reported-has-violation"
                  value="no"
                  checked={step3.reportedHasViolation === 'no'}
                  onChange={() => setStep3({ reportedHasViolation: 'no' })}
                />
                <span className="custom-control-label">خیر</span>
              </Form.Label>
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reported-has-violation"
                  value="yes"
                  checked={step3.reportedHasViolation === 'yes'}
                  onChange={() => setStep3({ reportedHasViolation: 'yes' })}
                />
                <span className="custom-control-label">بلی</span>
              </Form.Label>
            </div>
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedHasViolation ? 'block' : 'none' }}
            >
              {validationErrors.reportedHasViolation}
            </div>
          </Form.Group>

          {step3.reportedHasViolation === 'yes' && (
            <Fragment>
              <Form.Group className="form-group">
                <Form.Label className="form-label">تخلف فعلی تکرار تخلفات قبلی است</Form.Label>
                <div className="d-flex flex-row align-items-center gap-4">
                  <Form.Label className="custom-control custom-radio">
                    <Form.Control
                      type="radio"
                      className="custom-control-input"
                      name="reported-repeat-violation"
                      value="yes"
                      checked={step3.reportedRepeatViolation === 'yes'}
                      onChange={() => setStep3({ reportedRepeatViolation: 'yes' })}
                    />
                    <span className="custom-control-label">بلی</span>
                  </Form.Label>
                  <Form.Label className="custom-control custom-radio">
                    <Form.Control
                      type="radio"
                      className="custom-control-input"
                      name="reported-repeat-violation"
                      value="no"
                      checked={step3.reportedRepeatViolation === 'no'}
                      onChange={() => setStep3({ reportedRepeatViolation: 'no' })}
                    />
                    <span className="custom-control-label">خیر</span>
                  </Form.Label>
                </div>
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedRepeatViolation ? 'block' : 'none' }}
                >
                  {validationErrors.reportedRepeatViolation}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">انواع تخلفاتی که تاکنون انجام داده است</Form.Label>
                <Form.Control
                  id="reported-violation-types"
                  type="text"
                  className="form-control"
                  required
                  placeholder="انواع تخلفات را وارد کنید"
                  value={step3.reportedViolationTypes || ''}
                  onChange={(e) => setStep3({ reportedViolationTypes: e.target.value })}
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedViolationTypes ? 'block' : 'none' }}
                >
                  {validationErrors.reportedViolationTypes}
                </div>
              </Form.Group>
              <Form.Group className="form-group">
                <Form.Label className="form-label">تخلفات قبلی در چند موسسه مختلف بوده‌اند</Form.Label>
                <div className="d-flex flex-row align-items-center gap-4">
                  <Form.Label className="custom-control custom-radio">
                    <Form.Control
                      type="radio"
                      className="custom-control-input"
                      name="reported-multi-institute-violations"
                      value="yes"
                      checked={step3.reportedMultiInstituteViolations === 'yes'}
                      onChange={() => setStep3({ reportedMultiInstituteViolations: 'yes' })}
                    />
                    <span className="custom-control-label">بلی</span>
                  </Form.Label>
                  <Form.Label className="custom-control custom-radio">
                    <Form.Control
                      type="radio"
                      className="custom-control-input"
                      name="reported-multi-institute-violations"
                      value="no"
                      checked={step3.reportedMultiInstituteViolations === 'no'}
                      onChange={() => setStep3({ reportedMultiInstituteViolations: 'no' })}
                    />
                    <span className="custom-control-label">خیر</span>
                  </Form.Label>
                </div>
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedMultiInstituteViolations ? 'block' : 'none' }}
                >
                  {validationErrors.reportedMultiInstituteViolations}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">سابقه تخلفات قبلی (شماره پرونده‌ها)</Form.Label>
                <Form.Control
                  id="reported-violation-cases"
                  type="text"
                  className="form-control"
                  placeholder="شماره پرونده‌های تخلفات قبلی را وارد کنید"
                  value={step3.reportedViolationCases || ''}
                  onChange={(e) => setStep3({ reportedViolationCases: e.target.value })}
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.reportedViolationCases ? 'block' : 'none' }}
                >
                  {validationErrors.reportedViolationCases}
                </div>
              </Form.Group>
            </Fragment>
          )}

          {/* موسسه محل وقوع تخلف */}
          {step3.reportedViolationInstitutes.map((institute, index) => (
            <Fragment key={index}>
              <h5 className={'mt-5'}>موسسه محل وقوع تخلف {index !== 0 ? index + 1 : ''}</h5>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">نام دانشگاه</Form.Label>
                <Select
                  options={universityOptions}
                  placeholder="دانشگاه را انتخاب کنید"
                  classNamePrefix="Select2"
                  value={universityOptions.find(
                    (option) => option.value === step3.reportedViolationInstitutes[index].university
                  )}
                  isSearchable
                  onChange={(e) =>
                    setStep3({
                      reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                        i === index
                          ? {
                              ...inst,
                              university: e.value,
                              customUniversity: e.value === 'other' ? '' : inst.customUniversity,
                            }
                          : inst
                      ),
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors[`institute_university_${index}`] ? 'block' : 'none' }}
                >
                  {validationErrors[`institute_university_${index}`]}
                </div>
              </Form.Group>
              {institute.university === 'other' && (
                <Form.Group className="control-group form-group">
                  <Form.Label className="form-label">نام موسسه</Form.Label>
                  <Form.Control
                    id={`reported-violation-custom-university-${index}`}
                    type="text"
                    className="form-control"
                    required
                    placeholder="نام موسسه"
                    value={institute.customUniversity || ''}
                    onChange={(e) =>
                      setStep3({
                        reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                          i === index ? { ...inst, customUniversity: e.target.value } : inst
                        ),
                      })
                    }
                  />
                  <div
                    className="invalid-feedback"
                    style={{ display: validationErrors[`institute_customUniversity_${index}`] ? 'block' : 'none' }}
                  >
                    {validationErrors[`institute_customUniversity_${index}`]}
                  </div>
                </Form.Group>
              )}
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">سطح تیپ دانشگاه</Form.Label>
                <Select
                  options={[
                    { value: '', label: 'انتخاب کنید' },
                    { value: 'type1', label: 'یک' },
                    { value: 'type2', label: 'دو' },
                    { value: 'type3', label: 'سه' },
                    { value: 'non_medical', label: 'موسسات غیر علوم پزشکی' },
                  ]}
                  value={[
                    { value: '', label: 'انتخاب کنید' },
                    { value: 'type1', label: 'یک' },
                    { value: 'type2', label: 'دو' },
                    { value: 'type3', label: 'سه' },
                    { value: 'non_medical', label: 'موسسات غیر علوم پزشکی' },
                  ].find((option) => option.value === step3.reportedViolationInstitutes[index].universityType)}
                  placeholder="سطح تیپ دانشگاه را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  onChange={(e) =>
                    setStep3({
                      reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                        i === index ? { ...inst, universityType: e.value } : inst
                      ),
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors[`institute_universityType_${index}`] ? 'block' : 'none' }}
                >
                  {validationErrors[`institute_universityType_${index}`]}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">نوع موسسه</Form.Label>
                <Select
                  options={[
                    { value: '', label: 'انتخاب کنید' },
                    { value: 'faculty', label: 'دانشکده' },
                    { value: 'research_center', label: 'مرکز تحقیقات' },
                    { value: 'research_institute', label: 'پژوهشگاه' },
                    { value: 'hospital', label: 'بیمارستان' },
                    { value: 'other', label: 'سایر' },
                  ]}
                  value={[
                    { value: '', label: 'انتخاب کنید' },
                    { value: 'faculty', label: 'دانشکده' },
                    { value: 'research_center', label: 'مرکز تحقیقات' },
                    { value: 'research_institute', label: 'پژوهشگاه' },
                    { value: 'hospital', label: 'بیمارستان' },
                    { value: 'other', label: 'سایر' },
                  ].find((option) => option.value === step3.reportedViolationInstitutes[index]?.instituteType)}
                  placeholder="نوع موسسه را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  onChange={(e) =>
                    setStep3({
                      reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                        i === index ? { ...inst, instituteType: e.value } : inst
                      ),
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors[`institute_instituteType_${index}`] ? 'block' : 'none' }}
                >
                  {validationErrors[`institute_instituteType_${index}`]}
                </div>
              </Form.Group>
              <Form.Group className="form-group">
                <Form.Label className="form-label">محل جغرافیایی موسسه</Form.Label>
                <div className="d-flex flex-row align-items-center gap-4">
                  <Form.Label className="custom-control custom-radio">
                    <Form.Control
                      type="radio"
                      className="custom-control-input"
                      name={`institute-location-${index}`}
                      value="domestic"
                      checked={institute.location === 'domestic'}
                      onChange={() =>
                        setStep3({
                          reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                            i === index ? { ...inst, location: 'domestic' } : inst
                          ),
                        })
                      }
                    />
                    <span className="custom-control-label">داخل کشور</span>
                  </Form.Label>
                  <Form.Label className="custom-control custom-radio">
                    <Form.Control
                      type="radio"
                      className="custom-control-input"
                      name={`institute-location-${index}`}
                      value="international"
                      checked={institute.location === 'international'}
                      onChange={() =>
                        setStep3({
                          reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                            i === index ? { ...inst, location: 'international' } : inst
                          ),
                        })
                      }
                    />
                    <span className="custom-control-label">خارج کشور</span>
                  </Form.Label>
                </div>
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors[`institute_location_${index}`] ? 'block' : 'none' }}
                >
                  {validationErrors[`institute_location_${index}`]}
                </div>
              </Form.Group>
              <Form.Group className="form-group">
                <Form.Label className="form-label">موسسه دارای کارگروه اخلاق در پژوهش است</Form.Label>
                <div className="d-flex flex-row align-items-center gap-4">
                  <Form.Label className="custom-control custom-radio">
                    <Form.Control
                      type="radio"
                      className="custom-control-input"
                      name={`institute-ethics-committee-${index}`}
                      value="yes"
                      checked={institute.hasEthicsCommittee === 'yes'}
                      onChange={() =>
                        setStep3({
                          reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                            i === index ? { ...inst, hasEthicsCommittee: 'yes' } : inst
                          ),
                        })
                      }
                    />
                    <span className="custom-control-label">بلی</span>
                  </Form.Label>
                  <Form.Label className="custom-control custom-radio">
                    <Form.Control
                      type="radio"
                      className="custom-control-input"
                      name={`institute-ethics-committee-${index}`}
                      value="no"
                      checked={institute.hasEthicsCommittee === 'no'}
                      onChange={() =>
                        setStep3({
                          reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                            i === index ? { ...inst, hasEthicsCommittee: 'no' } : inst
                          ),
                        })
                      }
                    />
                    <span className="custom-control-label">خیر</span>
                  </Form.Label>
                </div>
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors[`institute_hasEthicsCommittee_${index}`] ? 'block' : 'none' }}
                >
                  {validationErrors[`institute_hasEthicsCommittee_${index}`]}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">رابطه سازمانی گزارش شونده با موسسه محل وقوع تخلف</Form.Label>
                <Select
                  options={[
                    { value: '', label: 'انتخاب کنید' },
                    { value: 'faculty', label: 'هیات علمی' },
                    { value: 'employee', label: 'کارمند' },
                    { value: 'student', label: 'دانشجو' },
                    { value: 'guest_researcher', label: 'پژوهشگر مهمان' },
                    { value: 'invited_faculty', label: 'هیات علمی مدعو' },
                    { value: 'other', label: 'سایر' },
                  ]}
                  value={[
                    { value: '', label: 'انتخاب کنید' },
                    { value: 'faculty', label: 'هیات علمی' },
                    { value: 'employee', label: 'کارمند' },
                    { value: 'student', label: 'دانشجو' },
                    { value: 'guest_researcher', label: 'پژوهشگر مهمان' },
                    { value: 'invited_faculty', label: 'هیات علمی مدعو' },
                    { value: 'other', label: 'سایر' },
                  ].find((option) => option.value === step3.reportedViolationInstitutes[index].reportedRelation)}
                  placeholder="رابطه سازمانی را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  onChange={(e) =>
                    setStep3({
                      reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                        i === index ? { ...inst, reportedRelation: e.value } : inst
                      ),
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors[`institute_reportedRelation_${index}`] ? 'block' : 'none' }}
                >
                  {validationErrors[`institute_reportedRelation_${index}`]}
                </div>
              </Form.Group>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">رابطه سازمانی گزارش‌دهنده با موسسه محل وقوع تخلف</Form.Label>
                <Select
                  options={[
                    { value: '', label: 'انتخاب کنید' },
                    { value: 'faculty', label: 'هیات علمی' },
                    { value: 'employee', label: 'کارمند' },
                    { value: 'student', label: 'دانشجو' },
                    { value: 'guest_researcher', label: 'پژوهشگر مهمان' },
                    { value: 'invited_faculty', label: 'هیات علمی مدعو' },
                    { value: 'other', label: 'سایر' },
                  ]}
                  value={[
                    { value: '', label: 'انتخاب کنید' },
                    { value: 'faculty', label: 'هیات علمی' },
                    { value: 'employee', label: 'کارمند' },
                    { value: 'student', label: 'دانشجو' },
                    { value: 'guest_researcher', label: 'پژوهشگر مهمان' },
                    { value: 'invited_faculty', label: 'هیات علمی مدعو' },
                    { value: 'other', label: 'سایر' },
                  ].find((option) => option.value === step3.reportedRelation)}
                  placeholder="رابطه سازمانی را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  onChange={(e) =>
                    setStep3({
                      reportedViolationInstitutes: step3.reportedViolationInstitutes.map((inst, i) =>
                        i === index ? { ...inst, reporterRelation: e.value } : inst
                      ),
                    })
                  }
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors[`institute_reporterRelation_${index}`] ? 'block' : 'none' }}
                >
                  {validationErrors[`institute_reporterRelation_${index}`]}
                </div>
              </Form.Group>
              {index > 0 && (
                <Button
                  variant="danger"
                  onClick={() =>
                    setStep3({
                      reportedViolationInstitutes: step3.reportedViolationInstitutes.filter((_, i) => i !== index),
                    })
                  }
                >
                  حذف موسسه
                </Button>
              )}
            </Fragment>
          ))}

          <Form.Group className="form-group">
            <Form.Label className="form-label">تخلف فعلی در چند موسسه مختلف واقع شده است</Form.Label>
            <div className="d-flex flex-row align-items-center gap-4">
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reported-violation-multi-institutes"
                  value="yes"
                  checked={step3.reportedViolationMultipleInstitutes === 'yes'}
                  onChange={() =>
                    setStep3({
                      reportedViolationMultipleInstitutes: 'yes',
                      reportedViolationInstitutes: [
                        ...step3.reportedViolationInstitutes,
                        {
                          university: '',
                          customUniversity: '',
                          universityType: '',
                          instituteType: '',
                          location: '',
                          hasEthicsCommittee: '',
                          reportedRelation: '',
                          reporterRelation: '',
                        },
                      ],
                    })
                  }
                />
                <span className="custom-control-label">بلی</span>
              </Form.Label>
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reported-violation-multi-institutes"
                  value="no"
                  checked={step3.reportedViolationMultipleInstitutes === 'no'}
                  onChange={() =>
                    setStep3({
                      reportedViolationMultipleInstitutes: 'no',
                      reportedViolationInstitutes: [
                        {
                          university: '',
                          customUniversity: '',
                          universityType: '',
                          instituteType: '',
                          location: '',
                          hasEthicsCommittee: '',
                          reportedRelation: '',
                          reporterRelation: '',
                        },
                      ],
                    })
                  }
                />
                <span className="custom-control-label">خیر</span>
              </Form.Label>
            </div>
          </Form.Group>
        </section>
      </Step>
      <Step title="نحوه گزارش تخلف" number="۴">
        <section className="card-body Basicwizard">
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">تاریخ دریافت گزارش توسط کارگروه</Form.Label>
            <Form.Control
              id="report-received-date"
              type="date"
              size="sm"
              className="form-control"
              required
              value={step4.reportReceivedDate || ''}
              onChange={(e) => setStep4({ reportReceivedDate: e.target.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportReceivedDate ? 'block' : 'none' }}
            >
              {validationErrors.reportReceivedDate}
            </div>
          </Form.Group>

          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">تخلف چه مدت پس از وقوع، گزارش شده است</Form.Label>
            <Select
              options={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'less_than_one_year', label: 'کمتر از یکسال' },
                { value: 'one_year', label: 'یکسال' },
                { value: 'two_years', label: 'دو سال' },
                { value: 'more_than_two_years', label: 'بیش از دو سال' },
              ]}
              placeholder="مدت زمان را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              onChange={(e) => setStep4({ reportDelay: e.value })}
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reportDelay ? 'block' : 'none' }}>
              {validationErrors.reportDelay}
            </div>
          </Form.Group>

          <Form.Group className="form-group">
            <Form.Label className="form-label">نحوه اعلام گزارش تخلف به کارگروه</Form.Label>
            <div className="d-flex flex-row align-items-center gap-4">
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="report-method"
                  value="self_report"
                  checked={step4.reportMethod === 'self_report'}
                  onChange={() => setStep4({ reportMethod: 'self_report' })}
                />
                <span className="custom-control-label">خودگزارشی فرد متخلف</span>
              </Form.Label>
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="report-method"
                  value="others_whistleblowing"
                  checked={step4.reportMethod === 'others_whistleblowing'}
                  onChange={() => setStep4({ reportMethod: 'others_whistleblowing' })}
                />
                <span className="custom-control-label">افشاگری دیگران</span>
              </Form.Label>
            </div>
            <div className="invalid-feedback" style={{ display: validationErrors.reportMethod ? 'block' : 'none' }}>
              {validationErrors.reportMethod}
            </div>
          </Form.Group>

          {step4.reportMethod === 'others_whistleblowing' && (
            <Fragment>
              <Form.Group className="control-group form-group">
                <Form.Label className="form-label">منبع افشاگری</Form.Label>
                <Select
                  options={[
                    { value: '', label: 'انتخاب کنید' },
                    {
                      value: 'journal_editor_reviewer_reader',
                      label: 'مجله منتشرکننده پژوهش (دبیر یا داور یا خوانندگان)',
                    },
                    { value: 'scientific_council_reviewer', label: 'داور شورای علمی' },
                    { value: 'ethical_reviewer', label: 'داور اخلاقی پژوهش' },
                    { value: 'institute_manager', label: 'مدیر و مسئول موسسه محل انجام پژوهش' },
                    { value: 'institute_colleagues', label: 'همکاران در موسسه' },
                    { value: 'research_collaborators', label: 'همکاران آن پژوهش (همکار طرح پژوهشی)' },
                    { value: 'co_author', label: 'نویسنده همکار در مقاله' },
                    { value: 'student', label: 'دانشجو' },
                    { value: 'thesis_team_professors', label: 'استادان تیم پایان‌نامه' },
                    { value: 'research_project_leader', label: 'مجری طرح پژوهشی' },
                    { value: 'research_participants', label: 'شرکت‌کنندگان در پژوهش (بیمار یا آزمودنی‌ها)' },
                    { value: 'research_sponsor', label: 'حامی مالی پژوهش' },
                    { value: 'news_media', label: 'رسانه‌های خبری' },
                    { value: 'research_social_networks', label: 'شبکه‌های اجتماعی پژوهشی' },
                    { value: 'pubpeer', label: 'Pubpeer' },
                    { value: 'independent_whistleblowers', label: 'افشاگران مستقل جامعه' },
                    { value: 'anonymous', label: 'فرد ناشناس' },
                    { value: 'international_websites', label: 'سایت‌های بین‌المللی' },
                    { value: 'other', label: 'سایر' },
                  ]}
                  placeholder="منبع افشاگری را انتخاب کنید"
                  classNamePrefix="Select2"
                  isSearchable
                  onChange={(e) => setStep4({ whistleblowerSource: e.value })}
                />
                <div
                  className="invalid-feedback"
                  style={{ display: validationErrors.whistleblowerSource ? 'block' : 'none' }}
                >
                  {validationErrors.whistleblowerSource}
                </div>
              </Form.Group>
            </Fragment>
          )}

          <Form.Group className="form-group">
            <Form.Label className="form-label">هویت گزارش‌دهنده</Form.Label>
            <div className="d-flex flex-row align-items-center gap-4">
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reporter-identity"
                  value="known"
                  checked={step4.reporterIdentity === 'known'}
                  onChange={() => setStep4({ reporterIdentity: 'known' })}
                />
                <span className="custom-control-label">معلوم</span>
              </Form.Label>
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reporter-identity"
                  value="unknown"
                  checked={step4.reporterIdentity === 'unknown'}
                  onChange={() => setStep4({ reporterIdentity: 'unknown' })}
                />
                <span className="custom-control-label">نامعلوم</span>
              </Form.Label>
            </div>
            <div className="invalid-feedback" style={{ display: validationErrors.reporterIdentity ? 'block' : 'none' }}>
              {validationErrors.reporterIdentity}
            </div>
          </Form.Group>

          <Form.Group className="form-group">
            <Form.Label className="form-label">نقش گزارش‌دهنده تخلف پژوهشی</Form.Label>
            <div className="d-flex flex-row align-items-center gap-4">
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reporter-role"
                  value="whistleblower"
                  checked={step4.reporterViolationRole === 'whistleblower'}
                  onChange={() => setStep4({ reporterViolationRole: 'whistleblower' })}
                />
                <span className="custom-control-label">افشاگر (whistleblower)</span>
              </Form.Label>
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reporter-role"
                  value="complainant"
                  checked={step4.reporterViolationRole === 'complainant'}
                  onChange={() => setStep4({ reporterViolationRole: 'complainant' })}
                />
                <span className="custom-control-label">شاکی (complainant)</span>
              </Form.Label>
            </div>
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reporterViolationRole ? 'block' : 'none' }}
            >
              {validationErrors.reporterViolationRole}
            </div>
          </Form.Group>

          <Form.Group className="form-group">
            <Form.Label className="form-label">ذینفعی گزارش‌دهنده در این پژوهش</Form.Label>
            <div className="d-flex flex-row align-items-center gap-4">
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reporter-stakeholder"
                  value="stakeholder"
                  checked={step4.reporterStakeholder === 'stakeholder'}
                  onChange={() => setStep4({ reporterStakeholder: 'stakeholder' })}
                />
                <span className="custom-control-label">ذینفع</span>
              </Form.Label>
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="reporter-stakeholder"
                  value="non_stakeholder"
                  checked={step4.reporterStakeholder === 'non_stakeholder'}
                  onChange={() => setStep4({ reporterStakeholder: 'non_stakeholder' })}
                />
                <span className="custom-control-label">غیر ذینفع</span>
              </Form.Label>
            </div>
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reporterStakeholder ? 'block' : 'none' }}
            >
              {validationErrors.reporterStakeholder}
            </div>
          </Form.Group>

          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">رابطه گزارش‌دهنده با گزارش‌شونده</Form.Label>
            <Select
              options={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'professor_student', label: 'استاد-دانشجو' },
                { value: 'manager_employee', label: 'رئیس-کارمند' },
                { value: 'research_collaborator', label: 'همکار پژوهشی' },
                { value: 'patient_doctor', label: 'بیمار-پزشک' },
                { value: 'other', label: 'سایر' },
              ]}
              placeholder="رابطه را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              onChange={(e) => setStep4({ reporterReportedRelation: e.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reporterReportedRelation ? 'block' : 'none' }}
            >
              {validationErrors.reporterReportedRelation}
            </div>
          </Form.Group>

          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">مرجع دریافت کننده گزارش تخلف</Form.Label>
            <Select
              options={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'institute_ethics_committee', label: 'کارگروه اخلاق در پژوهش موسسه' },
                { value: 'ministry_ethics_committee', label: 'کارگروه اخلاق در پژوهش وزارتخانه' },
                { value: 'security_office', label: 'حراست' },
                { value: 'inspection_complaints_office', label: 'اداره بازرسی و پاسخگویی به شکایات' },
                { value: 'violation_review_board', label: 'هیات‌های رسیدگی به تخلف' },
                { value: 'other', label: 'سایر' },
              ]}
              placeholder="مرجع را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              onChange={(e) => setStep4({ reportRecipient: e.value })}
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reportRecipient ? 'block' : 'none' }}>
              {validationErrors.reportRecipient}
            </div>
          </Form.Group>

          <Form.Group className="form-group">
            <Form.Label className="form-label">شکل گزارش تخلف</Form.Label>
            <div className="d-flex flex-row align-items-center gap-4">
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="report-format"
                  value="written"
                  checked={step4.reportFormat === 'written'}
                  onChange={() => setStep4({ reportFormat: 'written' })}
                />
                <span className="custom-control-label">کتبی</span>
              </Form.Label>
              <Form.Label className="custom-control custom-radio">
                <Form.Control
                  type="radio"
                  className="custom-control-input"
                  name="report-format"
                  value="electronic"
                  checked={step4.reportFormat === 'electronic'}
                  onChange={() => setStep4({ reportFormat: 'electronic' })}
                />
                <span className="custom-control-label">الکترونیکی</span>
              </Form.Label>
            </div>
            <div className="invalid-feedback" style={{ display: validationErrors.reportFormat ? 'block' : 'none' }}>
              {validationErrors.reportFormat}
            </div>
          </Form.Group>

          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">مسیر ارسال گزارش تخلف</Form.Label>
            <Select
              options={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'phone_call', label: 'تماس تلفنی' },
                { value: 'email', label: 'ایمیل' },
                { value: 'official_letter', label: 'نامه رسمی' },
                { value: 'social_media', label: 'شبکه‌های اجتماعی مجازی' },
              ]}
              placeholder="مسیر ارسال را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              onChange={(e) => setStep4({ reportChannel: e.value })}
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reportChannel ? 'block' : 'none' }}>
              {validationErrors.reportChannel}
            </div>
          </Form.Group>

          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">گزارش تخلف پژوهشی در کدام مرحله انجام پژوهش</Form.Label>
            <Select
              options={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'before_research', label: 'پیش از شروع پژوهش' },
                { value: 'during_research', label: 'حین انجام پژوهش' },
                { value: 'after_research_publication', label: 'پس از پایان پژوهش و انتشار آن' },
              ]}
              placeholder="مرحله پژوهش را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              onChange={(e) => setStep4({ researchStage: e.value })}
            />
            <div className="invalid-feedback" style={{ display: validationErrors.researchStage ? 'block' : 'none' }}>
              {validationErrors.researchStage}
            </div>
          </Form.Group>
        </section>
      </Step>
      <Step title="شرح تخلف توسط گزارش‌دهنده" number="۵">
        <section className="card-body Basicwizard">
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">شرح گزارش تخلف</Form.Label>
            <Form.Control
              id="violation-description"
              as="textarea"
              rows={5}
              size="sm"
              className="form-control"
              required
              placeholder="شرح تخلف را وارد کنید"
              value={step5.violationDescription || ''}
              onChange={(e) => setStep5({ violationDescription: e.target.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.violationDescription ? 'block' : 'none' }}
            >
              {validationErrors.violationDescription}
            </div>
          </Form.Group>

          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">مستندات ارائه شده</Form.Label>
            <Form.Control
              id="violation-documents"
              type="file"
              className="form-control"
              multiple
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
              onChange={(e) => setStep5({ violationDocuments: e.target.files })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.violationDocuments ? 'block' : 'none' }}
            >
              {validationErrors.violationDocuments}
            </div>
          </Form.Group>
        </section>
      </Step>
      <Step title="شرح تخلف توسط گزارش شونده" number="۶">
        <section className="card-body Basicwizard">
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">توضیحات و دفاعیات گزارش‌شونده</Form.Label>
            <Form.Control
              id="reported-defense-description"
              as="textarea"
              rows={5}
              size="sm"
              className="form-control"
              required
              placeholder="توضیحات و دفاعیات گزارش‌شونده را وارد کنید"
              value={step6.reportedDefenseDescription || ''}
              onChange={(e) => setStep6({ reportedDefenseDescription: e.target.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedDefenseDescription ? 'block' : 'none' }}
            >
              {validationErrors.reportedDefenseDescription}
            </div>
          </Form.Group>

          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">دعوتنامه و مستندات مربوطه</Form.Label>
            <Form.Control
              id="reported-defense-documents"
              type="file"
              className="form-control"
              multiple
              accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
              onChange={(e) => setStep6({ reportedDefenseDocuments: e.target.files })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reportedDefenseDocuments ? 'block' : 'none' }}
            >
              {validationErrors.reportedDefenseDocuments}
            </div>
          </Form.Group>
        </section>
      </Step>
    </Wizard>
  );
};

FormWizard.propTypes = {};

export default FormWizard;
