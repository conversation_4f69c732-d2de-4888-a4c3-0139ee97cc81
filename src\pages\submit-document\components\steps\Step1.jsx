import { useState, useEffect, useContext } from 'react';
import { Form } from 'react-bootstrap';
import Select from 'react-select';
import useSubmitDocumentFormStore from 'store/submitDocumentFormStore.js';
import AuthContext from 'context/auth-context';

const Step1 = ({ validationErrors }) => {
  const { step1, setStep1 } = useSubmitDocumentFormStore();
  const { isLoading, profile } = useContext(AuthContext);

  // State for institutes list and loading
  const [institutes, setInstitutes] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch institutes on component mount
  useEffect(() => {
    // const fetchInstitutes = async () => {
    //   try {
    //     setLoading(true);
    //     const response = await InstituteService.getMyInstitute();
    //     const instituteData = response?.data?.data?.institutes || [];

    //     // Transform the data to match Select component format
    //     const instituteOptions = [
    //       { value: '', label: 'انتخاب کنید' },
    //       ...instituteData.map((institute) => ({
    //         value: institute.uuid,
    //         label: institute.title,
    //       })),
    //       // { value: 'other', label: 'سایر' },
    //     ];

    //     setInstitutes(instituteOptions);
    //   } catch (error) {
    //     console.error('Error fetching institutes:', error);
    //     toastService.error('خطا در دریافت لیست کارگروه‌ها');
    //     // Fallback to default options
    //     setInstitutes([
    //       { value: '', label: 'انتخاب کنید' },
    //       // { value: 'other', label: 'سایر' },
    //     ]);
    //   } finally {
    //     setLoading(false);
    //   }
    // };

    const instituteOptions = [
      { value: '', label: 'انتخاب کنید' },
      { value: profile.institute?.uuid, label: profile.institute?.title },
    ];
    setInstitutes(instituteOptions);
  }, []);

  return (
    <section className="card-body Basicwizard">
      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">انتخاب کارگروه</Form.Label>
        <Select
          options={institutes}
          placeholder={loading ? 'در حال بارگذاری...' : 'کارگروه خود را انتخاب کنید'}
          classNamePrefix="Select2"
          isSearchable
          isLoading={loading}
          isDisabled={loading}
          value={institutes.find((option) => option.value === step1.organ) || null}
          onChange={(e) =>
            setStep1({
              organ: e?.value || '',
              customOrgan: e?.value === 'other' ? '' : step1.customOrgan,
            })
          }
        />
        <div className="invalid-feedback" style={{ display: validationErrors.organ ? 'block' : 'none' }}>
          {validationErrors.organ}
        </div>
      </Form.Group>

      {/* {step1.organ === 'other' && (
        <Form.Group className="control-group form-group">
          <Form.Label className="form-label">نام کارگروه</Form.Label>
          <Form.Control
            id={'organ-name'}
            type="text"
            className="form-control"
            required
            placeholder="نام کارگروه"
            value={step1.customOrgan}
            onChange={(e) =>
              setStep1({
                customOrgan: e.target.value,
              })
            }
          />
          <div className="invalid-feedback" style={{ display: validationErrors.customOrgan ? 'block' : 'none' }}>
            {validationErrors.customOrgan}
          </div>
        </Form.Group>
      )} */}
    </section>
  );
};

export default Step1;
