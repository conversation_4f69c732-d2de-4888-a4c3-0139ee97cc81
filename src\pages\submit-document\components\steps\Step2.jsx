import React, { Fragment, useState, useEffect } from 'react';
import { Form, FormGroup } from 'react-bootstrap';
import Select from 'react-select';
import useSubmitDocumentFormStore from 'store/submitDocumentFormStore.js';
import InstituteService from 'service/api/instituteService.js';
import toastService from 'utils/toastService.js';

const Step2 = ({ validationErrors }) => {
  const { step2, setStep2 } = useSubmitDocumentFormStore();

  // State for institutes list and loading
  const [institutes, setInstitutes] = useState([]);
  const [loading, setLoading] = useState(false);

  const educationLevelOptions = [
    { value: '', label: 'انتخاب کنید' },
    { value: 'phd', label: 'دکترای تخصصی (PHD)' },
    { value: 'professional_doctorate', label: 'دکترای حرفه‌ای' },
    { value: 'master', label: 'کارشناسی ارشد' },
    { value: 'bachelor', label: 'کارشناسی' },
    { value: 'associate', label: 'کاردانی' },
    { value: 'clinical_specialty', label: 'تخصص / فوق تخصص بالینی' },
  ];

  // Fetch institutes on component mount
  useEffect(() => {
    const fetchInstitutes = async () => {
      try {
        setLoading(true);
        const response = await InstituteService.getMyInstitute();
        const instituteData = response?.data?.data?.institutes || [];

        // Transform the data to match Select component format
        const instituteOptions = [
          { value: '', label: 'انتخاب کنید' },
          ...instituteData.map((institute) => ({
            value: institute.uuid,
            label: institute.title,
          })),
          // { value: 'other', label: 'سایر' },
        ];

        setInstitutes(instituteOptions);
      } catch (error) {
        console.error('Error fetching institutes:', error);
        toastService.error('خطا در دریافت لیست موسسات');
        // Fallback to default options
        setInstitutes([
          { value: '', label: 'انتخاب کنید' },
          { value: 'other', label: 'سایر' },
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchInstitutes();
  }, []);

  return (
    <section className="card-body Basicwizard ">
      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">نام و نام خانوادگی</Form.Label>
        <Form.Control
          id={'reporter-fullname'}
          type="text"
          size="sm"
          className="form-control"
          required
          placeholder="نام و نام خانوادگی را وارد کنید"
          value={step2.reporterFullname}
          onChange={(e) =>
            setStep2({
              reporterFullname: e.target.value,
            })
          }
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reporterFullname ? 'block' : 'none' }}>
          {validationErrors.reporterFullname}
        </div>
      </Form.Group>
      <FormGroup className="form-group">
        <Form.Label className="form-label">جنسیت</Form.Label>
        <div className="d-flex flex-row align-items-center gap-4">
          {/* Option for "مجرد" */}
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reporter-gender"
              value="مرد"
              checked={step2.reporterGender === 'male'}
              onChange={() => {
                setStep2({ reporterGender: 'male' });
              }}
            />
            <span className="custom-control-label">مرد</span>
          </Form.Label>

          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reporter-gender"
              value="زن"
              checked={step2.reporterGender === 'female'}
              onChange={() => {
                setStep2({ reporterGender: 'female' });
              }}
            />
            <span className="custom-control-label">زن</span>
          </Form.Label>
        </div>
        <div className="invalid-feedback" style={{ display: validationErrors.reporterGender ? 'block' : 'none' }}>
          {validationErrors.reporterGender}
        </div>
      </FormGroup>
      <FormGroup className="form-group">
        <Form.Label htmlFor="reporter-nationalID">کد ملی</Form.Label>
        <Form.Control
          id={'reporter-nationalID'}
          type="text"
          size="sm"
          className="form-control"
          placeholder="کد ملی را وارد کنید"
          value={step2.reporterNationalID}
          onChange={(e) =>
            setStep2({
              reporterNationalID: e.target.value,
            })
          }
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reporterNationalID ? 'block' : 'none' }}>
          {validationErrors.reporterNationalID}
        </div>
      </FormGroup>
      <FormGroup className="form-group">
        <Form.Label>شماره تماس</Form.Label>
        <Form.Control
          id={'reporter-phone'}
          type="text"
          size="sm"
          className="form-control"
          placeholder="شماره تماس را وارد کنید"
          value={step2.reporterPhone}
          onChange={(e) =>
            setStep2({
              reporterPhone: e.target.value,
            })
          }
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reporterPhone ? 'block' : 'none' }}>
          {validationErrors.reporterPhone}
        </div>
      </FormGroup>
      <FormGroup className="form-group">
        <Form.Label>رایانامه</Form.Label>
        <Form.Control
          id={'reporter-email'}
          type="text"
          size="sm"
          className="form-control"
          placeholder="رایانامه خود را وارد کنید"
          value={step2.reporterEmail}
          onChange={(e) =>
            setStep2({
              reporterEmail: e.target.value,
            })
          }
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reporterEmail ? 'block' : 'none' }}>
          {validationErrors.reporterEmail}
        </div>
      </FormGroup>
      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">موسسه محل خدمت</Form.Label>
        <Select
          options={institutes}
          placeholder={loading ? 'در حال بارگذاری...' : 'موسسه خود را انتخاب کنید'}
          classNamePrefix="Select2"
          isSearchable
          isLoading={loading}
          isDisabled={loading}
          value={institutes.find((option) => option.value === step2.reporterOrgan) || null}
          onChange={(e) =>
            setStep2({
              reporterOrgan: e?.value || '',
              reporterCustomOrgan: e?.value === 'other' ? '' : step2.reporterCustomOrgan,
            })
          }
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reporterOrgan ? 'block' : 'none' }}>
          {validationErrors.reporterOrgan}
        </div>
      </Form.Group>
      {step2.reporterOrgan === 'other' && (
        <Form.Group className="control-group form-group">
          <Form.Label className="form-label">نام موسسه</Form.Label>
          <Form.Control
            id={'reporter-organ-name'}
            type="text"
            className="form-control"
            required
            placeholder="نام موسسه"
            value={step2.reporterCustomOrgan}
            onChange={(e) =>
              setStep2({
                reporterCustomOrgan: e.target.value,
              })
            }
          />
          <div
            className="invalid-feedback"
            style={{ display: validationErrors.reporterCustomOrgan ? 'block' : 'none' }}
          >
            {validationErrors.reporterCustomOrgan}
          </div>
        </Form.Group>
      )}

      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">نقش حرفه‌ای و آکادمیک</Form.Label>
        <Select
          options={[
            { value: '', label: 'انتخاب کنید' },
            { value: 'faculty_member', label: 'عضو هیئت علمی' },
            { value: 'student', label: 'دانشجو' },
            { value: 'non_faculty_member', label: 'اعضای غیر هیئت علمی' },
            { value: 'independent_researcher', label: 'پژوهشگر آزاد' },
          ]}
          placeholder="نقش آکادمیک خود را انتخاب کنید"
          classNamePrefix="Select2"
          isSearchable
          value={[
            { value: '', label: 'انتخاب کنید' },
            { value: 'faculty_member', label: 'عضو هیئت علمی' },
            { value: 'student', label: 'دانشجو' },
            { value: 'non_faculty_member', label: 'اعضای غیر هیئت علمی' },
            { value: 'independent_researcher', label: 'پژوهشگر آزاد' },
          ].find((option) => option.value === step2.reporterRole)}
          onChange={(e) =>
            setStep2({
              reporterRole: e.value,
              reporterGrade: '',
              reporterField: '',
              reporterYearsOfService: '',
              reporterEducationLevel: '',
              reporterAcademicStatus: '',
              reporterAdministrativePosition: '',
            })
          }
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reporterRole ? 'block' : 'none' }}>
          {validationErrors.reporterRole}
        </div>
      </Form.Group>
      {step2.reporterRole === 'faculty_member' && (
        <Fragment>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">مرتبه علمی</Form.Label>
            <Form.Control
              id="reporter-faculty-grade"
              type="text"
              className="form-control"
              required
              placeholder="مرتبه علمی خود را وارد کنید"
              value={step2.reporterGrade || ''}
              onChange={(e) =>
                setStep2({
                  reporterGrade: e.target.value,
                })
              }
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reporterGrade ? 'block' : 'none' }}>
              {validationErrors.reporterGrade}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">رشته تحصیلی</Form.Label>
            <Form.Control
              id="reporter-faculty-field"
              type="text"
              className="form-control"
              required
              placeholder="رشته تحصیلی خود را وارد کنید"
              value={step2.reporterField || ''}
              onChange={(e) =>
                setStep2({
                  reporterField: e.target.value,
                })
              }
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reporterField ? 'block' : 'none' }}>
              {validationErrors.reporterField}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">سنوات خدمت</Form.Label>
            <Form.Control
              id="reporter-faculty-years-of-service"
              type="text"
              className="form-control"
              required
              placeholder="سنوات خدمت خود را وارد کنید"
              value={step2.reporterYearsOfService || ''}
              onChange={(e) =>
                setStep2({
                  reporterYearsOfService: e.target.value,
                })
              }
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reporterYearsOfService ? 'block' : 'none' }}
            >
              {validationErrors.reporterYearsOfService}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
            <Select
              options={educationLevelOptions}
              placeholder="مقطع تحصیلی خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={educationLevelOptions.find((option) => option.value === step2.reporterEducationLevel)}
              onChange={(e) =>
                setStep2({
                  reporterEducationLevel: e.value,
                })
              }
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reporterEducationLevel ? 'block' : 'none' }}
            >
              {validationErrors.reporterEducationLevel}
            </div>
          </Form.Group>
        </Fragment>
      )}
      {step2.reporterRole === 'student' && (
        <Fragment>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">رشته تحصیلی</Form.Label>
            <Form.Control
              id="reporter-student-field"
              type="text"
              className="form-control"
              required
              placeholder="رشته تحصیلی خود را وارد کنید"
              value={step2.reporterField || ''}
              onChange={(e) =>
                setStep2({
                  reporterField: e.target.value,
                })
              }
            />
            <div className="invalid-feedback" style={{ display: validationErrors.reporterField ? 'block' : 'none' }}>
              {validationErrors.reporterField}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
            <Select
              options={educationLevelOptions}
              placeholder="مقطع تحصیلی خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={educationLevelOptions.find((option) => option.value === step2.reporterEducationLevel)}
              onChange={(e) =>
                setStep2({
                  reporterEducationLevel: e.value,
                })
              }
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reporterEducationLevel ? 'block' : 'none' }}
            >
              {validationErrors.reporterEducationLevel}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">وضعیت تحصیلی</Form.Label>
            <Select
              options={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'graduated', label: 'فارغ‌التحصیل' },
                { value: 'studying', label: 'شاغل به تحصیل' },
              ]}
              value={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'graduated', label: 'فارغ‌التحصیل' },
                { value: 'studying', label: 'شاغل به تحصیل' },
              ].find((option) => option.value === step2.reporterAcademicStatus)}
              placeholder="وضعیت تحصیلی خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              onChange={(e) =>
                setStep2({
                  reporterAcademicStatus: e.value,
                })
              }
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reporterAcademicStatus ? 'block' : 'none' }}
            >
              {validationErrors.reporterAcademicStatus}
            </div>
          </Form.Group>
        </Fragment>
      )}

      {/* Fragment for Non-Faculty Member */}
      {step2.reporterRole === 'non_faculty_member' && (
        <Fragment>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">سمت اجرایی</Form.Label>
            <Form.Control
              id="reporter-non-faculty-admin-position"
              type="text"
              className="form-control"
              required
              placeholder="سمت اجرایی خود را وارد کنید"
              value={step2.reporterAdministrativePosition || ''}
              onChange={(e) =>
                setStep2({
                  reporterAdministrativePosition: e.target.value,
                })
              }
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reporterAdministrativePosition ? 'block' : 'none' }}
            >
              {validationErrors.reporterAdministrativePosition}
            </div>
          </Form.Group>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
            <Select
              options={educationLevelOptions}
              placeholder="مقطع تحصیلی خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={educationLevelOptions.find((option) => option.value === step2.reporterEducationLevel)}
              onChange={(e) =>
                setStep2({
                  reporterEducationLevel: e.value,
                })
              }
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reporterEducationLevel ? 'block' : 'none' }}
            >
              {validationErrors.reporterEducationLevel}
            </div>
          </Form.Group>
        </Fragment>
      )}

      {/* Fragment for Independent Researcher */}
      {step2.reporterRole === 'independent_researcher' && (
        <Fragment>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">مقطع تحصیلی</Form.Label>
            <Select
              options={educationLevelOptions}
              placeholder="مقطع تحصیلی خود را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={educationLevelOptions.find((option) => option.value === step2.reporterEducationLevel)}
              onChange={(e) =>
                setStep2({
                  reporterEducationLevel: e.value,
                })
              }
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.reporterEducationLevel ? 'block' : 'none' }}
            >
              {validationErrors.reporterEducationLevel}
            </div>
          </Form.Group>
        </Fragment>
      )}
    </section>
  );
};

export default Step2;
