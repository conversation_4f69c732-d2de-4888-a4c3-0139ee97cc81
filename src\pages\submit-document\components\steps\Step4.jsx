import React, { Fragment } from 'react';
import { Form, FormGroup } from 'react-bootstrap';
import Select from 'react-select';
import useSubmitDocumentFormStore from 'store/submitDocumentFormStore.js';

const Step4 = ({ validationErrors }) => {
  const { step4, setStep4 } = useSubmitDocumentFormStore();

  return (
    <section className="card-body Basicwizard">
      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">تاریخ دریافت گزارش توسط کارگروه</Form.Label>
        <Form.Control
          id="report-received-date"
          type="date"
          size="sm"
          className="form-control"
          required
          value={step4.reportReceivedDate || ''}
          onChange={(e) => setStep4({ reportReceivedDate: e.target.value })}
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reportReceivedDate ? 'block' : 'none' }}>
          {validationErrors.reportReceivedDate}
        </div>
      </Form.Group>

      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">تخلف چه مدت پس از وقوع، گزارش شده است</Form.Label>
        <Select
          options={[
            { value: '', label: 'انتخاب کنید' },
            { value: 'less_than_one_year', label: 'کمتر از یکسال' },
            { value: 'one_year', label: 'یکسال' },
            { value: 'two_years', label: 'دو سال' },
            { value: 'more_than_two_years', label: 'بیش از دو سال' },
          ]}
          placeholder="مدت زمان را انتخاب کنید"
          classNamePrefix="Select2"
          isSearchable
          value={[
            { value: '', label: 'انتخاب کنید' },
            { value: 'less_than_one_year', label: 'کمتر از یکسال' },
            { value: 'one_year', label: 'یکسال' },
            { value: 'two_years', label: 'دو سال' },
            { value: 'more_than_two_years', label: 'بیش از دو سال' },
          ].find((option) => option.value === step4.reportDelay)}
          onChange={(e) => setStep4({ reportDelay: e.value })}
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reportDelay ? 'block' : 'none' }}>
          {validationErrors.reportDelay}
        </div>
      </Form.Group>

      <Form.Group className="form-group">
        <Form.Label className="form-label">نحوه اعلام گزارش تخلف به کارگروه</Form.Label>
        <div className="d-flex flex-row align-items-center gap-4">
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="report-method"
              value="self_report"
              checked={step4.reportMethod === 'self_report'}
              onChange={() => setStep4({ reportMethod: 'self_report' })}
            />
            <span className="custom-control-label">خودگزارشی فرد متخلف</span>
          </Form.Label>
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="report-method"
              value="whistleblower"
              checked={step4.reportMethod === 'whistleblower'}
              onChange={() => setStep4({ reportMethod: 'whistleblower' })}
            />
            <span className="custom-control-label">افشاگری دیگران</span>
          </Form.Label>
        </div>
        <div className="invalid-feedback" style={{ display: validationErrors.reportMethod ? 'block' : 'none' }}>
          {validationErrors.reportMethod}
        </div>
      </Form.Group>

      {step4.reportMethod === 'whistleblower' && (
        <Fragment>
          <Form.Group className="control-group form-group">
            <Form.Label className="form-label">منبع افشاگری</Form.Label>
            <Select
              options={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'journal_editor', label: 'مجله منتشرکننده پژوهش (دبیر)' },
                { value: 'journal_reviewer', label: 'مجله منتشرکننده پژوهش (داور)' },
                { value: 'journal_reader', label: 'مجله منتشرکننده پژوهش (خوانندگان)' },
                { value: 'scientific_council_reviewer', label: 'داور شورای علمی' },
                { value: 'ethical_reviewer', label: 'داور اخلاقی پژوهش' },
                { value: 'institute_manager', label: 'مدیر و مسئول موسسه محل انجام پژوهش' },
                { value: 'institute_colleagues', label: 'همکاران در موسسه' },
                { value: 'research_colleagues', label: 'همکاران آن پژوهش (همکار طرح پژوهشی)' },
                { value: 'co_author', label: 'نویسنده همکار در مقاله' },
                { value: 'student', label: 'دانشجو' },
                { value: 'thesis_advisor', label: 'استادان تیم پایان‌نامه' },
                { value: 'research_lead', label: 'مجری طرح پژوهشی' },
                { value: 'research_participant', label: 'شرکت‌کنندگان در پژوهش (بیمار یا آزمودنی‌ها)' },
                { value: 'research_sponsor', label: 'حامی مالی پژوهش' },
                { value: 'news_media', label: 'رسانه‌های خبری' },
                { value: 'research_social_media', label: 'شبکه‌های اجتماعی پژوهشی' },
                { value: 'pubpeer', label: 'Pubpeer' },
                { value: 'independent_whistleblower', label: 'افشاگران مستقل جامعه' },
                { value: 'anonymous', label: 'فرد ناشناس' },
                { value: 'international_site', label: 'سایت‌های بین‌المللی' },
                { value: 'other', label: 'سایر' },
              ]}
              placeholder="منبع افشاگری را انتخاب کنید"
              classNamePrefix="Select2"
              isSearchable
              value={[
                { value: '', label: 'انتخاب کنید' },
                { value: 'journal_editor', label: 'مجله منتشرکننده پژوهش (دبیر)' },
                { value: 'journal_reviewer', label: 'مجله منتشرکننده پژوهش (داور)' },
                { value: 'journal_reader', label: 'مجله منتشرکننده پژوهش (خوانندگان)' },
                { value: 'scientific_council_reviewer', label: 'داور شورای علمی' },
                { value: 'ethical_reviewer', label: 'داور اخلاقی پژوهش' },
                { value: 'institute_manager', label: 'مدیر و مسئول موسسه محل انجام پژوهش' },
                { value: 'institute_colleagues', label: 'همکاران در موسسه' },
                { value: 'research_colleagues', label: 'همکاران آن پژوهش (همکار طرح پژوهشی)' },
                { value: 'co_author', label: 'نویسنده همکار در مقاله' },
                { value: 'student', label: 'دانشجو' },
                { value: 'thesis_advisor', label: 'استادان تیم پایان‌نامه' },
                { value: 'research_lead', label: 'مجری طرح پژوهشی' },
                { value: 'research_participant', label: 'شرکت‌کنندگان در پژوهش (بیمار یا آزمودنی‌ها)' },
                { value: 'research_sponsor', label: 'حامی مالی پژوهش' },
                { value: 'news_media', label: 'رسانه‌های خبری' },
                { value: 'research_social_media', label: 'شبکه‌های اجتماعی پژوهشی' },
                { value: 'pubpeer', label: 'Pubpeer' },
                { value: 'independent_whistleblower', label: 'افشاگران مستقل جامعه' },
                { value: 'anonymous', label: 'فرد ناشناس' },
                { value: 'international_site', label: 'سایت‌های بین‌المللی' },
                { value: 'other', label: 'سایر' },
              ].find((option) => option.value === step4.whistleblowerSource)}
              onChange={(e) => setStep4({ whistleblowerSource: e.value })}
            />
            <div
              className="invalid-feedback"
              style={{ display: validationErrors.whistleblowerSource ? 'block' : 'none' }}
            >
              {validationErrors.whistleblowerSource}
            </div>
          </Form.Group>
        </Fragment>
      )}

      <Form.Group className="form-group">
        <Form.Label className="form-label">هویت گزارش‌دهنده</Form.Label>
        <div className="d-flex flex-row align-items-center gap-4">
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reporter-identity"
              value="known"
              checked={step4.reporterIdentity === 'known'}
              onChange={() => setStep4({ reporterIdentity: 'known' })}
            />
            <span className="custom-control-label">معلوم</span>
          </Form.Label>
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reporter-identity"
              value="unknown"
              checked={step4.reporterIdentity === 'unknown'}
              onChange={() => setStep4({ reporterIdentity: 'unknown' })}
            />
            <span className="custom-control-label">نامعلوم</span>
          </Form.Label>
        </div>
        <div className="invalid-feedback" style={{ display: validationErrors.reporterIdentity ? 'block' : 'none' }}>
          {validationErrors.reporterIdentity}
        </div>
      </Form.Group>

      <Form.Group className="form-group">
        <Form.Label className="form-label">نقش گزارش‌دهنده تخلف پژوهشی</Form.Label>
        <div className="d-flex flex-row align-items-center gap-4">
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reporter-role"
              value="whistleblower"
              checked={step4.reporterViolationRole === 'whistleblower'}
              onChange={() => setStep4({ reporterViolationRole: 'whistleblower' })}
            />
            <span className="custom-control-label">افشاگر (whistleblower)</span>
          </Form.Label>
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reporter-role"
              value="complainant"
              checked={step4.reporterViolationRole === 'complainant'}
              onChange={() => setStep4({ reporterViolationRole: 'complainant' })}
            />
            <span className="custom-control-label">شاکی (complainant)</span>
          </Form.Label>
        </div>
        <div
          className="invalid-feedback"
          style={{ display: validationErrors.reporterViolationRole ? 'block' : 'none' }}
        >
          {validationErrors.reporterViolationRole}
        </div>
      </Form.Group>

      <Form.Group className="form-group">
        <Form.Label className="form-label">ذینفعی گزارش‌دهنده در این پژوهش</Form.Label>
        <div className="d-flex flex-row align-items-center gap-4">
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reporter-stakeholder"
              value="stakeholder"
              checked={step4.reporterStakeholder === 'stakeholder'}
              onChange={() => setStep4({ reporterStakeholder: 'stakeholder' })}
            />
            <span className="custom-control-label">ذینفع</span>
          </Form.Label>
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="reporter-stakeholder"
              value="non_stakeholder"
              checked={step4.reporterStakeholder === 'non_stakeholder'}
              onChange={() => setStep4({ reporterStakeholder: 'non_stakeholder' })}
            />
            <span className="custom-control-label">غیر ذینفع</span>
          </Form.Label>
        </div>
        <div className="invalid-feedback" style={{ display: validationErrors.reporterStakeholder ? 'block' : 'none' }}>
          {validationErrors.reporterStakeholder}
        </div>
      </Form.Group>

      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">رابطه گزارش‌دهنده با گزارش‌شونده</Form.Label>
        <Select
          options={[
            { value: '', label: 'انتخاب کنید' },
            { value: 'professor_student', label: 'استاد-دانشجو' },
            { value: 'manager_employee', label: 'رئیس-کارمند' },
            { value: 'research_collaborator', label: 'همکار پژوهشی' },
            { value: 'patient_doctor', label: 'بیمار-پزشک' },
            { value: 'other', label: 'سایر' },
          ]}
          placeholder="رابطه را انتخاب کنید"
          classNamePrefix="Select2"
          isSearchable
          value={[
            { value: '', label: 'انتخاب کنید' },
            { value: 'professor_student', label: 'استاد-دانشجو' },
            { value: 'manager_employee', label: 'رئیس-کارمند' },
            { value: 'research_collaborator', label: 'همکار پژوهشی' },
            { value: 'patient_doctor', label: 'بیمار-پزشک' },
            { value: 'other', label: 'سایر' },
          ].find((option) => option.value === step4.reporterReportedRelation)}
          onChange={(e) => setStep4({ reporterReportedRelation: e.value })}
        />
        <div
          className="invalid-feedback"
          style={{ display: validationErrors.reporterReportedRelation ? 'block' : 'none' }}
        >
          {validationErrors.reporterReportedRelation}
        </div>
      </Form.Group>

      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">مرجع دریافت کننده گزارش تخلف</Form.Label>
        <Select
          options={[
            { value: '', label: 'انتخاب کنید' },
            { value: 'institute_ethics_committee', label: 'کارگروه اخلاق در پژوهش موسسه' },
            { value: 'ministry_ethics_committee', label: 'کارگروه اخلاق در پژوهش وزارتخانه' },
            { value: 'security_office', label: 'حراست' },
            { value: 'inspection_office', label: 'اداره بازرسی و پاسخگویی به شکایات' },
            { value: 'violation_committee', label: 'هیات‌های رسیدگی به تخلف' },
            { value: 'other', label: 'سایر' },
          ]}
          placeholder="مرجع را انتخاب کنید"
          classNamePrefix="Select2"
          isSearchable
          value={[
            { value: '', label: 'انتخاب کنید' },
            { value: 'institute_ethics_committee', label: 'کارگروه اخلاق در پژوهش موسسه' },
            { value: 'ministry_ethics_committee', label: 'کارگروه اخلاق در پژوهش وزارتخانه' },
            { value: 'security_office', label: 'حراست' },
            { value: 'inspection_office', label: 'اداره بازرسی و پاسخگویی به شکایات' },
            { value: 'violation_committee', label: 'هیات‌های رسیدگی به تخلف' },
            { value: 'other', label: 'سایر' },
          ].find((option) => option.value === step4.reportRecipient)}
          onChange={(e) => setStep4({ reportRecipient: e.value })}
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reportRecipient ? 'block' : 'none' }}>
          {validationErrors.reportRecipient}
        </div>
      </Form.Group>

      <Form.Group className="form-group">
        <Form.Label className="form-label">شکل گزارش تخلف</Form.Label>
        <div className="d-flex flex-row align-items-center gap-4">
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="report-format"
              value="written"
              checked={step4.reportFormat === 'written'}
              onChange={() => setStep4({ reportFormat: 'written' })}
            />
            <span className="custom-control-label">کتبی</span>
          </Form.Label>
          <Form.Label className="custom-control custom-radio">
            <Form.Control
              type="radio"
              className="custom-control-input"
              name="report-format"
              value="electronic"
              checked={step4.reportFormat === 'electronic'}
              onChange={() => setStep4({ reportFormat: 'electronic' })}
            />
            <span className="custom-control-label">الکترونیکی</span>
          </Form.Label>
        </div>
        <div className="invalid-feedback" style={{ display: validationErrors.reportFormat ? 'block' : 'none' }}>
          {validationErrors.reportFormat}
        </div>
      </Form.Group>

      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">مسیر ارسال گزارش تخلف</Form.Label>
        <Select
          options={[
            { value: '', label: 'انتخاب کنید' },
            { value: 'phone_call', label: 'تماس تلفنی' },
            { value: 'email', label: 'ایمیل' },
            { value: 'formal_letter', label: 'نامه رسمی' },
            { value: 'social_media', label: 'شبکه‌های اجتماعی مجازی' },
          ]}
          placeholder="مسیر ارسال را انتخاب کنید"
          classNamePrefix="Select2"
          isSearchable
          value={[
            { value: '', label: 'انتخاب کنید' },
            { value: 'phone_call', label: 'تماس تلفنی' },
            { value: 'email', label: 'ایمیل' },
            { value: 'formal_letter', label: 'نامه رسمی' },
            { value: 'social_media', label: 'شبکه‌های اجتماعی مجازی' },
          ].find((option) => option.value === step4.reportChannel)}
          onChange={(e) => setStep4({ reportChannel: e.value })}
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reportChannel ? 'block' : 'none' }}>
          {validationErrors.reportChannel}
        </div>
      </Form.Group>

      <Form.Group className="control-group form-group">
        <Form.Label className="form-label">گزارش تخلف پژوهشی در کدام مرحله انجام پژوهش</Form.Label>
        <Select
          options={[
            { value: '', label: 'انتخاب کنید' },
            { value: 'before_research', label: 'پیش از شروع پژوهش' },
            { value: 'during_research', label: 'حین انجام پژوهش' },
            { value: 'after_publication', label: 'پس از پایان پژوهش و انتشار آن' },
          ]}
          placeholder="مرحله پژوهش را انتخاب کنید"
          classNamePrefix="Select2"
          isSearchable
          value={[
            { value: '', label: 'انتخاب کنید' },
            { value: 'before_research', label: 'پیش از شروع پژوهش' },
            { value: 'during_research', label: 'حین انجام پژوهش' },
            { value: 'after_publication', label: 'پس از پایان پژوهش و انتشار آن' },
          ].find((option) => option.value === step4.researchStage)}
          onChange={(e) => setStep4({ researchStage: e.value })}
        />
        <div className="invalid-feedback" style={{ display: validationErrors.researchStage ? 'block' : 'none' }}>
          {validationErrors.researchStage}
        </div>
      </Form.Group>
    </section>
  );
};

export default Step4;
