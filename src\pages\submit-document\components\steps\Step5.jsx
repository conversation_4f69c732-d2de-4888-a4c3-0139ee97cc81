import React from 'react';
import { Form, FormGroup } from 'react-bootstrap';
import useSubmitDocumentFormStore from 'store/submitDocumentFormStore.js';

const Step5 = ({ validationErrors }) => {
  const { step5, setStep5 } = useSubmitDocumentFormStore();

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setStep5({
      ...step5,
      violationDocuments: [...(step5.violationDocuments || []), ...files],
    });
  };

  const removeDocument = (index) => {
    const updatedDocs = [...step5.violationDocuments];
    updatedDocs.splice(index, 1);
    setStep5({
      ...step5,
      violationDocuments: updatedDocs,
    });
  };

  return (
    <section className="card-body Basicwizard">
      <FormGroup className="form-group">
        <Form.Label>شرح گزارش تخلف</Form.Label>
        <Form.Control
          as="textarea"
          rows={5}
          className="form-control"
          placeholder="شرح تخلف را وارد کنید"
          value={step5.reporterDescription || ''}
          onChange={(e) =>
            setStep5({
              ...step5,
              reporterDescription: e.target.value,
            })
          }
        />
        <div className="invalid-feedback" style={{ display: validationErrors.reporterDescription ? 'block' : 'none' }}>
          {validationErrors.reporterDescription}
        </div>
      </FormGroup>

      <FormGroup className="form-group">
        <Form.Label className="form-label">مستندات ارائه شده</Form.Label>
        <Form.Control type="file" className="form-control" multiple onChange={handleFileChange} />
        <div className="invalid-feedback" style={{ display: validationErrors.violationDocuments ? 'block' : 'none' }}>
          {validationErrors.violationDocuments}
        </div>
      </FormGroup>

      {step5.violationDocuments && step5.violationDocuments.length > 0 && (
        <div className="mt-3">
          <h6>فایل‌های آپلود شده:</h6>
          <ul className="list-group">
            {step5.violationDocuments.map((file, index) => (
              <li key={index} className="list-group-item d-flex justify-content-between align-items-center">
                {file.name}
                <button type="button" className="btn btn-sm btn-danger" onClick={() => removeDocument(index)}>
                  حذف
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </section>
  );
};

export default Step5;
