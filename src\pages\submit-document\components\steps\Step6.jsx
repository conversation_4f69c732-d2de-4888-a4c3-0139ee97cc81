import React from 'react';
import { Form, FormGroup } from 'react-bootstrap';
import useSubmitDocumentFormStore from 'store/submitDocumentFormStore.js';

const Step6 = ({ validationErrors }) => {
  const { step6, setStep6 } = useSubmitDocumentFormStore();

  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setStep6({
      ...step6,
      reportedDefenseDocuments: [...(step6.reportedDefenseDocuments || []), ...files],
    });
  };

  const removeDocument = (index) => {
    const updatedDocs = [...step6.reportedDefenseDocuments];
    updatedDocs.splice(index, 1);
    setStep6({
      ...step6,
      reportedDefenseDocuments: updatedDocs,
    });
  };

  return (
    <section className="card-body Basicwizard">
      <FormGroup className="form-group">
        <Form.Label>توضیحات و دفاعیات</Form.Label>
        <Form.Control
          id="reported-defense-description"
          as="textarea"
          rows={5}
          className="form-control"
          placeholder="توضیحات و دفاعیات را وارد کنید"
          value={step6.reportedDefenseDescription || ''}
          onChange={(e) =>
            setStep6({
              ...step6,
              reportedDefenseDescription: e.target.value,
            })
          }
        />
        <div
          className="invalid-feedback"
          style={{ display: validationErrors.reportedDefenseDescription ? 'block' : 'none' }}
        >
          {validationErrors.reportedDefenseDescription}
        </div>
      </FormGroup>

      <FormGroup className="form-group">
        <Form.Label className="form-label">دعوتنامه و مستندات مربوطه</Form.Label>
        <Form.Control
          type="file"
          id="reported-defense-documents"
          className="form-control"
          multiple
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
          onChange={handleFileChange}
        />
        <div
          className="invalid-feedback"
          style={{ display: validationErrors.reportedDefenseDocuments ? 'block' : 'none' }}
        >
          {validationErrors.reportedDefenseDocuments}
        </div>
      </FormGroup>

      {step6.reportedDefenseDocuments && step6.reportedDefenseDocuments.length > 0 && (
        <div className="mt-3">
          <h6>فایل‌های آپلود شده:</h6>
          <ul className="list-group">
            {step6.reportedDefenseDocuments.map((file, index) => (
              <li key={index} className="list-group-item d-flex justify-content-between align-items-center">
                {file.name}
                <button type="button" className="btn btn-sm btn-danger" onClick={() => removeDocument(index)}>
                  حذف
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </section>
  );
};

export default Step6;
