import React, { useState, Fragment, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import { Button, Modal, Form } from 'react-bootstrap';
import AccountService from 'service/api/accountService.js';
import InstituteService from 'service/api/instituteService.js';
import toastService from 'utils/toastService.js';
import useGeneralStore from 'store/generalStore.js';
import UserList from './components/user-table.jsx';
import Select from 'react-select';

const Users = () => {
  const { setData } = useGeneralStore();

  // State management
  const [users, setUsers] = useState([]);
  const [institutes, setInstitutes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState(false);
  const [instituteMode, setInstituteMode] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);

  // Form state
  const [formData, setFormData] = useState({
    username: '',
    password1: '',
    password2: '',
    email: '',
    first_name: '',
    last_name: '',
    institute_uuid: '',
  });

  const [formErrors, setFormErrors] = useState({});

  // Fetch users from API
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);
      const response = await AccountService.getAccounts();
      const userData = response?.data?.data?.users || [];
      console.log(userData);
      setUsers(userData);
    } catch (error) {
      console.error('Error fetching users:', error);
      toastService.error('خطا در دریافت لیست کاربران');
      setUsers([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch institutes for dropdown
  const fetchInstitutes = useCallback(async () => {
    try {
      const response = await InstituteService.getList({ has_member: false });
      const instituteData = response?.data?.data?.institutes || [];

      const instituteOptions = [
        { value: '', label: 'انتخاب کنید' },
        ...instituteData.map((institute) => ({
          value: institute.uuid,
          label: institute.title,
        })),
      ];

      setInstitutes(instituteOptions);
    } catch (error) {
      console.error('Error fetching institutes:', error);
      setInstitutes([{ value: '', label: 'انتخاب کنید' }]);
    }
  }, []);

  useEffect(() => {
    setData({ pageTitle: 'مدیریت کاربران' });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Set page title only once on mount, setData is stable

  useEffect(() => {
    fetchUsers();
    fetchInstitutes();
  }, [fetchUsers, fetchInstitutes]); // Fetch data separately

  // Form validation
  const validateForm = () => {
    const errors = {};

    if (!instituteMode) {
      if (!formData.username.trim()) {
        errors.username = 'نام کاربری الزامی است';
      } else if (!/^[a-zA-Z0-9_-]+$/.test(formData.username)) {
        errors.username = 'نام کاربری فقط می‌تواند شامل حروف انگلیسی، اعداد، خط تیره (-) و زیرخط (_) باشد';
      } else if (formData.username.length < 3) {
        errors.username = 'نام کاربری باید حداقل ۳ کاراکتر باشد';
      } else if (formData.username.length > 30) {
        errors.username = 'نام کاربری نباید بیشتر از ۳۰ کاراکتر باشد';
      }

      if (!formData.email.trim()) {
        errors.email = 'ایمیل الزامی است';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        errors.email = 'فرمت ایمیل صحیح نیست';
      }

      if (!formData.first_name.trim()) {
        errors.first_name = 'نام الزامی است';
      }

      if (!formData.last_name.trim()) {
        errors.last_name = 'نام خانوادگی الزامی است';
      }
    }

    if (!editMode && !instituteMode) {
      if (!formData.password1.trim()) {
        errors.password1 = 'رمز عبور الزامی است';
      }

      if (!formData.password2.trim()) {
        errors.password2 = 'تکرار رمز عبور الزامی است';
      }

      if (formData.password1 !== formData.password2) {
        errors.password2 = 'رمز عبور و تکرار آن باید یکسان باشند';
      }
    }

    if (instituteMode) {
      if (!formData.institute_uuid) {
        errors.institute_uuid = 'انتخاب کارگروه الزامی است';
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Handle form input changes
  const handleInputChange = (field, value) => {
    // Username validation - only allow alphanumeric, underscore, and hyphen
    if (field === 'username') {
      // Remove any characters that are not alphanumeric, underscore, or hyphen
      value = value.replace(/[^a-zA-Z0-9_-]/g, '');
    }

    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear error when user starts typing
    if (formErrors[field]) {
      setFormErrors((prev) => ({
        ...prev,
        [field]: '',
      }));
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      username: '',
      password1: '',
      password2: '',
      email: '',
      first_name: '',
      last_name: '',
      institute_uuid: '',
    });
    setFormErrors({});
    setEditMode(false);
    setInstituteMode(false);
    setSelectedUser(null);
  };

  // Handle modal close
  const handleClose = () => {
    setShow(false);
    resetForm();
  };

  // Handle modal show for add
  const handleShow = () => {
    resetForm();
    setShow(true);
  };

  // Handle edit user
  const handleEditUser = useCallback((user) => {
    setFormData({
      username: user.username || '',
      // password1: '',
      // password2: '',
      email: user.email || '',
      first_name: user.first_name || '',
      last_name: user.last_name || '',
      // institute_uuid: user.institute_uuid || '',
    });
    setSelectedUser(user);
    setEditMode(true);
    setShow(true);
  }, []);

  const handleSetInstitute = useCallback((user) => {
    setFormData({
      institute_uuid: user.institute_uuid || '',
    });
    setSelectedUser(user);
    setInstituteMode(true);
    setShow(true);
  }, []);

  // Handle form submit
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);

      if (editMode && selectedUser) {
        // Update user
        const updateData = {
          first_name: formData.first_name,
          last_name: formData.last_name,
          email: formData.email,
        };

        await AccountService.updateAccount(selectedUser.uuid, updateData);
        toastService.success('کاربر با موفقیت به‌روزرسانی شد');
      } else if (instituteMode && selectedUser) {
        // Update user
        const updateData = {
          institute: formData.institute_uuid,
        };

        await AccountService.setInstituteAccount(selectedUser.uuid, updateData);
        fetchInstitutes();
        toastService.success('کارگروه کاربر با موفقیت به‌روزرسانی شد');
      } else {
        // Add new user
        await AccountService.addNewAccount(formData);
        toastService.success('کاربر جدید با موفقیت اضافه شد');
      }

      handleClose();
      fetchUsers(); // Refresh the list
    } catch (error) {
      console.error('Error saving user:', error);
      const errors = {};

      if (error.response?.data?.data) {
        for (const field in error.response.data.data[0]) {
          errors[field] = error.response.data.data[0][field][0];
        }
      } else {
        errors.non_field_errors = 'خطا در ارسال اطلاعات';
      }

      setFormErrors(errors);
      toastService.error(instituteMode || editMode ? 'خطا در به‌روزرسانی کاربر' : 'خطا در افزودن کاربر');
    } finally {
      setLoading(false);
    }
  };

  // Handle delete user
  const handleDeleteUser = useCallback(
    async (user) => {
      if (window.confirm('آیا از حذف این کاربر اطمینان دارید؟')) {
        try {
          setLoading(true);
          await AccountService.deleteAccount(user.uuid);
          toastService.success('کاربر با موفقیت حذف شد');
          fetchUsers(); // Refresh the list
        } catch (error) {
          console.error('Error deleting user:', error);
          toastService.error('خطا در حذف کاربر');
        } finally {
          setLoading(false);
        }
      }
    },
    [fetchUsers]
  );

  return (
    <Fragment>
      <div className="breadcrumb-header justify-content-between">
        <div className="left-content mt-2">
          <Link className="btn ripple btn-primary" to="#" onClick={handleShow}>
            <i className="fe fe-plus me-2"></i>افزودن کاربر جدید
          </Link>

          <Modal show={show} onHide={handleClose}>
            <Modal.Header className="modal-header">
              <h6 className="modal-title">{instituteMode || editMode ? 'ویرایش کاربر' : 'افزودن کاربر جدید'}</h6>
              <Button variant="" className="btn-close" type="button" onClick={handleClose}>
                <span aria-hidden="true">×</span>
              </Button>
            </Modal.Header>

            <Modal.Body className="modal-body">
              <div className="p-4">
                <Form className="form-horizontal">
                  {!editMode && !instituteMode && (
                    <>
                      <Form.Group className="form-group">
                        <Form.Label className="form-label">نام کاربری *</Form.Label>
                        <Form.Control
                          type="text"
                          className={`form-control ${formErrors.username ? 'is-invalid' : ''}`}
                          placeholder="نام کاربری را وارد کنید (فقط حروف انگلیسی، اعداد و _)"
                          value={formData.username}
                          onChange={(e) => handleInputChange('username', e.target.value)}
                          pattern="[a-zA-Z0-9_]+"
                          minLength="3"
                          maxLength="30"
                        />
                        {formErrors.username && <div className="invalid-feedback">{formErrors.username}</div>}
                      </Form.Group>

                      <Form.Group className="form-group">
                        <Form.Label className="form-label">رمز عبور *</Form.Label>
                        <Form.Control
                          type="password"
                          className={`form-control ${formErrors.password1 ? 'is-invalid' : ''}`}
                          placeholder="رمز عبور را وارد کنید"
                          value={formData.password1}
                          onChange={(e) => handleInputChange('password1', e.target.value)}
                        />
                        {formErrors.password1 && <div className="invalid-feedback">{formErrors.password1}</div>}
                      </Form.Group>

                      <Form.Group className="form-group">
                        <Form.Label className="form-label">تکرار رمز عبور *</Form.Label>
                        <Form.Control
                          type="password"
                          className={`form-control ${formErrors.password2 ? 'is-invalid' : ''}`}
                          placeholder="رمز عبور را مجدداً وارد کنید"
                          value={formData.password2}
                          onChange={(e) => handleInputChange('password2', e.target.value)}
                        />
                        {formErrors.password2 && <div className="invalid-feedback">{formErrors.password2}</div>}
                      </Form.Group>
                    </>
                  )}

                  {!instituteMode && (
                    <>
                      <Form.Group className="form-group">
                        <Form.Label className="form-label">نام *</Form.Label>
                        <Form.Control
                          type="text"
                          className={`form-control ${formErrors.first_name ? 'is-invalid' : ''}`}
                          placeholder="نام را وارد کنید"
                          value={formData.first_name}
                          onChange={(e) => handleInputChange('first_name', e.target.value)}
                        />
                        {formErrors.first_name && <div className="invalid-feedback">{formErrors.first_name}</div>}
                      </Form.Group>

                      <Form.Group className="form-group">
                        <Form.Label className="form-label">نام خانوادگی *</Form.Label>
                        <Form.Control
                          type="text"
                          className={`form-control ${formErrors.last_name ? 'is-invalid' : ''}`}
                          placeholder="نام خانوادگی را وارد کنید"
                          value={formData.last_name}
                          onChange={(e) => handleInputChange('last_name', e.target.value)}
                        />
                        {formErrors.last_name && <div className="invalid-feedback">{formErrors.last_name}</div>}
                      </Form.Group>

                      <Form.Group className="form-group">
                        <Form.Label className="form-label">ایمیل *</Form.Label>
                        <Form.Control
                          type="email"
                          className={`form-control ${formErrors.email ? 'is-invalid' : ''}`}
                          placeholder="ایمیل را وارد کنید"
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                        />
                        {formErrors.email && <div className="invalid-feedback">{formErrors.email}</div>}
                      </Form.Group>
                    </>
                  )}

                  {instituteMode && (
                    <Form.Group className="control-group form-group">
                      <Form.Label className="form-label">انتخاب کارگروه *</Form.Label>
                      <Select
                        options={institutes}
                        placeholder="کارگروه کاربر را انتخاب کنید"
                        classNamePrefix="Select2"
                        isSearchable
                        // value={formData.institute_uuid}
                        onChange={(e) => handleInputChange('institute_uuid', e?.value || '')}
                      />
                      {formErrors.institute_uuid && <div className="invalid-feedback">{formErrors.institute_uuid}</div>}
                    </Form.Group>
                  )}

                  {/* <Form.Group className="control-group form-group">
                    <Form.Label className="form-label">انتخاب نقش</Form.Label>
                    <Select
                      options={roleOptions}
                      placeholder="نقش کاربر را انتخاب کنید"
                      classNamePrefix="Select2"
                      isSearchable
                      value={roleOptions.find((option) => option.value === formData.role) || null}
                      onChange={(e) => handleInputChange('role', e?.value || '')}
                    />
                  </Form.Group> */}
                </Form>
                {formErrors.non_field_errors && <div className="invalid-feedback">{formErrors.non_field_errors}</div>}
              </div>
            </Modal.Body>
            <Modal.Footer>
              <Button
                variant=""
                className="btn ripple btn-primary"
                type="button"
                onClick={handleSubmit}
                disabled={loading}
              >
                {loading ? 'در حال پردازش...' : editMode || instituteMode ? 'به‌روزرسانی' : 'افزودن'}
              </Button>
              <Button variant="" className="btn ripple btn-secondary" onClick={handleClose}>
                بستن
              </Button>
            </Modal.Footer>
          </Modal>
        </div>
      </div>

      <UserList
        users={users}
        loading={loading}
        onEdit={handleEditUser}
        onDelete={handleDeleteUser}
        onSetInstitute={handleSetInstitute}
      />
    </Fragment>
  );
};

Users.propTypes = {};

export default Users;
