import useFetch from '../index';

class DossierService {
  getList() {
    return useFetch.get('/api/v1/dossier/list/');
  }

  addNewDossier(data) {
    return useFetch.post('/api/v1/dossier/new/', data);
  }

  updateDossier(uuid, data) {
    if (!uuid) return false;
    return useFetch.put(`/api/v1/dossier/new/${uuid}/`, data);
  }

  deleteDossier(uuid) {
    if (!uuid) return false;
    return useFetch.delete(`/api/v1/dossier/delete/${uuid}/`);
  }

  uploadDocument(formData) {
    return useFetch.post('api/v1/dossier/documents/upload/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }
}
export default new DossierService();
