import { create } from 'zustand';

const initialState = {
  step: 0,
  dossierUuid: null, // Track the dossier UUID after creation
  step1: {
    organ: '',
    customOrgan: '',
  },
  step2: {
    reporterFullname: '',
    reporterGender: '',
    reporterNationalID: '',
    reporterEmail: '',
    reporter<PERSON>hone: '',
    reporter<PERSON>rgan: '',
    reporter<PERSON><PERSON><PERSON><PERSON>rgan: '',
    reporterRole: '',
    reporterGrade: '',
    reporterYearsOfService: '',
    reporterEducationLevel: '',
    reporterField: '',
    reporterAcademicStatus: '',
    reporterAdministrativePosition: '',
  },
  step3: {
    reportedFullname: '',
    reportedGender: '',
    reportedNationalID: '',
    reportedPhone: '',
    reportedEmail: '',
    reportedOrgan: '',
    reportedCustomOrgan: '',
    reportedRole: '',
    reportedGrade: '',
    reportedField: '',
    reportedYearsOfService: '',
    reportedEducationLevel: '',
    reportedAcademicStatus: '',
    reportedAdministrativePosition: '',
    reportedHasViolation: '',
    reportedRepeatViolation: '',
    reportedViolationTypes: '',
    reportedMultiInstituteViolations: '',
    reportedViolationCases: '',
    reportedViolationMultipleInstitutes: 'no',
    reportedViolationInstitutes: [
      {
        university: '',
        customUniversity: '',
        universityType: '',
        instituteType: '',
        location: '',
        hasEthicsCommittee: '',
        reportedRelation: '',
        reporterRelation: '',
      },
    ],
  },
  step4: {
    reportReceivedDate: '',
    reportDelay: '',
    reportMethod: '',
    whistleblowerSource: '',
    reporterIdentity: '',
    reporterViolationRole: '',
    reporterStakeholder: '',
    reporterReportedRelation: '',
    reportRecipient: '',
    reportFormat: '',
    reportChannel: '',
    researchStage: '',
  },
  step5: {
    reporterDescription: '',
    violationDocuments: null,
  },
  step6: {
    reportedDefenseDescription: '',
    reportedDefenseDocuments: null,
  },
};

// Create Zustand store
const useSubmitDocumentFormStore = create((set, get) => ({
  ...initialState,
  setStep: (step) => set((state) => ({ ...state, step: step })),
  setDossierUuid: (uuid) => set((state) => ({ ...state, dossierUuid: uuid })),
  setStep1: (newForm) => set((state) => ({ ...state, step1: { ...state.step1, ...newForm } })),
  setStep2: (newForm) => set((state) => ({ ...state, step2: { ...state.step2, ...newForm } })),
  setStep3: (newForm) => set((state) => ({ ...state, step3: { ...state.step3, ...newForm } })),
  setStep4: (newForm) => set((state) => ({ ...state, step4: { ...state.step4, ...newForm } })),
  setStep5: (newForm) => set((state) => ({ ...state, step5: { ...state.step5, ...newForm } })),
  setStep6: (newForm) => set((state) => ({ ...state, step6: { ...state.step6, ...newForm } })),
  removeForm: (rest) => set((state) => ({ ...rest })),
  clearForm: () => {
    set({ initialState });
  },
}));

export default useSubmitDocumentFormStore;
